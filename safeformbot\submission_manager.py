"""
Submission Status Management System for SafeFormBot
Handles pending submissions, user confirmations, and status tracking
"""

import pandas as pd
import os
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading


class SubmissionManager:
    """Manages submission status and user confirmations"""
    
    def __init__(self):
        self.pending_file = "pending_submissions.csv"
        self.confirmed_file = "confirmed_submissions.csv"
        self.rejected_file = "rejected_submissions.csv"
        self.status_file = "submission_status.json"
        
    def add_pending_submission(self, row_data: Dict, timestamp: str = None) -> str:
        """Add a submission to pending status"""
        if not timestamp:
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        
        submission_id = f"{row_data.get('ID NO.', 'UNKNOWN')}_{timestamp}"
        
        pending_data = {
            'submission_id': submission_id,
            'ID NO.': row_data.get('ID NO.', ''),
            'First Name': row_data.get('First Name', ''),
            'Last Name': row_data.get('Last Name', ''),
            'timestamp': timestamp,
            'status': 'PENDING',
            'form_data': json.dumps(dict(row_data))
        }
        
        # Add to pending submissions file
        df_pending = pd.DataFrame([pending_data])
        if os.path.exists(self.pending_file):
            df_pending.to_csv(self.pending_file, mode='a', header=False, index=False)
        else:
            df_pending.to_csv(self.pending_file, mode='w', header=True, index=False)
        
        return submission_id
    
    def get_pending_submissions(self) -> pd.DataFrame:
        """Get all pending submissions"""
        if os.path.exists(self.pending_file):
            return pd.read_csv(self.pending_file)
        return pd.DataFrame()
    
    def confirm_submission(self, submission_id: str, user_notes: str = "") -> bool:
        """Confirm a pending submission"""
        return self._update_submission_status(submission_id, 'CONFIRMED', user_notes)
    
    def reject_submission(self, submission_id: str, reason: str = "") -> bool:
        """Reject a pending submission"""
        return self._update_submission_status(submission_id, 'REJECTED', reason)
    
    def _update_submission_status(self, submission_id: str, new_status: str, notes: str = "") -> bool:
        """Update submission status and move to appropriate file"""
        if not os.path.exists(self.pending_file):
            return False
        
        df_pending = pd.read_csv(self.pending_file)
        
        # Find the submission
        submission_row = df_pending[df_pending['submission_id'] == submission_id]
        if submission_row.empty:
            return False
        
        # Update the row
        updated_row = submission_row.copy()
        updated_row['status'] = new_status
        updated_row['confirmation_timestamp'] = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        updated_row['notes'] = notes
        
        # Move to appropriate file
        target_file = self.confirmed_file if new_status == 'CONFIRMED' else self.rejected_file
        
        if os.path.exists(target_file):
            updated_row.to_csv(target_file, mode='a', header=False, index=False)
        else:
            updated_row.to_csv(target_file, mode='w', header=True, index=False)
        
        # Remove from pending
        df_pending = df_pending[df_pending['submission_id'] != submission_id]
        df_pending.to_csv(self.pending_file, index=False)
        
        return True
    
    def get_submission_stats(self) -> Dict:
        """Get submission statistics"""
        stats = {
            'pending': 0,
            'confirmed': 0,
            'rejected': 0,
            'total': 0
        }
        
        # Count pending
        if os.path.exists(self.pending_file):
            df_pending = pd.read_csv(self.pending_file)
            stats['pending'] = len(df_pending)
        
        # Count confirmed
        if os.path.exists(self.confirmed_file):
            df_confirmed = pd.read_csv(self.confirmed_file)
            stats['confirmed'] = len(df_confirmed)
        
        # Count rejected
        if os.path.exists(self.rejected_file):
            df_rejected = pd.read_csv(self.rejected_file)
            stats['rejected'] = len(df_rejected)
        
        stats['total'] = stats['pending'] + stats['confirmed'] + stats['rejected']
        
        return stats
    
    def batch_confirm_submissions(self, submission_ids: List[str]) -> Dict:
        """Confirm multiple submissions at once"""
        results = {'success': [], 'failed': []}
        
        for submission_id in submission_ids:
            if self.confirm_submission(submission_id, "Batch confirmation"):
                results['success'].append(submission_id)
            else:
                results['failed'].append(submission_id)
        
        return results
    
    def export_status_report(self, filename: str = None) -> str:
        """Export a comprehensive status report"""
        if not filename:
            filename = f"submission_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        stats = self.get_submission_stats()
        
        report = []
        report.append("=== SAFEFORMBOT SUBMISSION STATUS REPORT ===")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        report.append("SUMMARY:")
        report.append(f"  Total Submissions: {stats['total']}")
        report.append(f"  Pending: {stats['pending']}")
        report.append(f"  Confirmed: {stats['confirmed']}")
        report.append(f"  Rejected: {stats['rejected']}")
        report.append("")
        
        # Pending submissions details
        if stats['pending'] > 0:
            report.append("PENDING SUBMISSIONS:")
            report.append("-" * 50)
            df_pending = self.get_pending_submissions()
            for _, row in df_pending.iterrows():
                report.append(f"ID: {row['submission_id']}")
                report.append(f"  Name: {row['First Name']} {row['Last Name']}")
                report.append(f"  ID NO.: {row['ID NO.']}")
                report.append(f"  Submitted: {row['timestamp']}")
                report.append("")
        
        # Recent confirmations
        if os.path.exists(self.confirmed_file):
            df_confirmed = pd.read_csv(self.confirmed_file)
            recent_confirmed = df_confirmed.tail(10)  # Last 10 confirmations
            
            if not recent_confirmed.empty:
                report.append("RECENT CONFIRMATIONS (Last 10):")
                report.append("-" * 50)
                for _, row in recent_confirmed.iterrows():
                    report.append(f"ID: {row['submission_id']}")
                    report.append(f"  Name: {row['First Name']} {row['Last Name']}")
                    report.append(f"  Confirmed: {row.get('confirmation_timestamp', 'N/A')}")
                    report.append("")
        
        report_text = "\n".join(report)
        
        with open(filename, 'w') as f:
            f.write(report_text)
        
        return filename


class SubmissionGUI:
    """GUI for managing submission confirmations"""
    
    def __init__(self):
        self.manager = SubmissionManager()
        self.root = None
        self.tree = None
        self.selected_items = []
        
    def start_gui(self):
        """Start the GUI application"""
        self.root = tk.Tk()
        self.root.title("SafeFormBot - Submission Manager")
        self.root.geometry("1000x600")
        
        self._create_widgets()
        self._load_data()
        
        self.root.mainloop()
    
    def _create_widgets(self):
        """Create GUI widgets"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Pending Submissions", font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=4, pady=(0, 10))
        
        # Stats frame
        stats_frame = ttk.LabelFrame(main_frame, text="Statistics", padding="5")
        stats_frame.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.stats_label = ttk.Label(stats_frame, text="Loading...")
        self.stats_label.grid(row=0, column=0)
        
        # Treeview for submissions
        tree_frame = ttk.Frame(main_frame)
        tree_frame.grid(row=2, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL)
        
        # Treeview
        self.tree = ttk.Treeview(tree_frame, 
                                columns=('ID', 'First Name', 'Last Name', 'ID NO.', 'Timestamp'),
                                show='headings',
                                yscrollcommand=v_scrollbar.set,
                                xscrollcommand=h_scrollbar.set)
        
        # Configure scrollbars
        v_scrollbar.config(command=self.tree.yview)
        h_scrollbar.config(command=self.tree.xview)
        
        # Column headings
        self.tree.heading('ID', text='Submission ID')
        self.tree.heading('First Name', text='First Name')
        self.tree.heading('Last Name', text='Last Name')
        self.tree.heading('ID NO.', text='ID NO.')
        self.tree.heading('Timestamp', text='Submitted')
        
        # Column widths
        self.tree.column('ID', width=200)
        self.tree.column('First Name', width=100)
        self.tree.column('Last Name', width=100)
        self.tree.column('ID NO.', width=100)
        self.tree.column('Timestamp', width=150)
        
        # Grid treeview and scrollbars
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # Configure grid weights
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=4, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="Confirm Selected", command=self._confirm_selected).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(buttons_frame, text="Reject Selected", command=self._reject_selected).grid(row=0, column=1, padx=5)
        ttk.Button(buttons_frame, text="Refresh", command=self._load_data).grid(row=0, column=2, padx=5)
        ttk.Button(buttons_frame, text="Export Report", command=self._export_report).grid(row=0, column=3, padx=5)
        ttk.Button(buttons_frame, text="Batch Confirm All", command=self._batch_confirm_all).grid(row=0, column=4, padx=(5, 0))
        
        # Configure main grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
    
    def _load_data(self):
        """Load pending submissions data"""
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Load pending submissions
        df_pending = self.manager.get_pending_submissions()
        
        for _, row in df_pending.iterrows():
            self.tree.insert('', tk.END, values=(
                row['submission_id'],
                row['First Name'],
                row['Last Name'],
                row['ID NO.'],
                row['timestamp']
            ))
        
        # Update stats
        stats = self.manager.get_submission_stats()
        stats_text = f"Pending: {stats['pending']} | Confirmed: {stats['confirmed']} | Rejected: {stats['rejected']} | Total: {stats['total']}"
        self.stats_label.config(text=stats_text)
    
    def _get_selected_submission_ids(self) -> List[str]:
        """Get selected submission IDs"""
        selected_items = self.tree.selection()
        submission_ids = []
        
        for item in selected_items:
            values = self.tree.item(item, 'values')
            if values:
                submission_ids.append(values[0])  # First column is submission_id
        
        return submission_ids
    
    def _confirm_selected(self):
        """Confirm selected submissions"""
        submission_ids = self._get_selected_submission_ids()
        
        if not submission_ids:
            messagebox.showwarning("No Selection", "Please select submissions to confirm.")
            return
        
        if messagebox.askyesno("Confirm", f"Confirm {len(submission_ids)} submission(s)?"):
            results = self.manager.batch_confirm_submissions(submission_ids)
            
            message = f"Confirmed: {len(results['success'])}"
            if results['failed']:
                message += f"\nFailed: {len(results['failed'])}"
            
            messagebox.showinfo("Results", message)
            self._load_data()
    
    def _reject_selected(self):
        """Reject selected submissions"""
        submission_ids = self._get_selected_submission_ids()
        
        if not submission_ids:
            messagebox.showwarning("No Selection", "Please select submissions to reject.")
            return
        
        # Get rejection reason
        reason = tk.simpledialog.askstring("Rejection Reason", "Enter reason for rejection (optional):")
        
        if messagebox.askyesno("Reject", f"Reject {len(submission_ids)} submission(s)?"):
            success_count = 0
            for submission_id in submission_ids:
                if self.manager.reject_submission(submission_id, reason or ""):
                    success_count += 1
            
            messagebox.showinfo("Results", f"Rejected: {success_count}/{len(submission_ids)}")
            self._load_data()
    
    def _batch_confirm_all(self):
        """Confirm all pending submissions"""
        df_pending = self.manager.get_pending_submissions()
        
        if df_pending.empty:
            messagebox.showinfo("No Data", "No pending submissions to confirm.")
            return
        
        if messagebox.askyesno("Confirm All", f"Confirm ALL {len(df_pending)} pending submissions?"):
            submission_ids = df_pending['submission_id'].tolist()
            results = self.manager.batch_confirm_submissions(submission_ids)
            
            message = f"Confirmed: {len(results['success'])}"
            if results['failed']:
                message += f"\nFailed: {len(results['failed'])}"
            
            messagebox.showinfo("Results", message)
            self._load_data()
    
    def _export_report(self):
        """Export status report"""
        filename = self.manager.export_status_report()
        messagebox.showinfo("Export Complete", f"Report exported to: {filename}")


def start_submission_gui():
    """Start the submission management GUI"""
    gui = SubmissionGUI()
    gui.start_gui()


if __name__ == "__main__":
    # Start the GUI
    start_submission_gui()
