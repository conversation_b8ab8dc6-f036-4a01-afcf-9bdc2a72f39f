"""
Test Data Pasting for SafeFormBot
This script fills the form with test data but DOES NOT submit
You can manually verify the data and submit yourself
"""

import pandas as pd
import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import pyperclip
from selenium.webdriver.common.keys import Keys
from config_manager import Config<PERSON>anager


def test_data_pasting():
    """Test data pasting without submission"""
    
    print("🧪 SafeFormBot - Data Pasting Test")
    print("=" * 50)
    
    # Load configuration
    print("📋 Loading configuration...")
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    if not config:
        print("❌ Failed to load configuration")
        return
    
    form_url = config.get('form_url')
    fields = config.get('fields', {})
    
    print(f"🌐 Form URL: {form_url}")
    print(f"📝 Fields to test: {len(fields)}")
    
    # Sample test data (first row from your CSV)
    test_data = {
        'ID NO.': '123456',
        'First Name': '<PERSON>',
        'Middle Initial': 'A',
        'Last Name': 'Doe',
        "Father's Name": 'Robert Doe',
        "Mother's Name": 'Mary Doe',
        "Mother's Maiden Name": 'Smith',
        'Age': '25.5',
        'Weight in Kgs.': '70',
        'Age in Company (Years)': '2.5',
        '% Hike Salary': '15%',
        'Register joining code': '123-45-6789',
        'Place Name': 'Springfield',
        'County': 'Clark',
        'City': 'Springfield',
        'Region': 'Midwest'
    }
    
    print("\n📊 Test Data:")
    for field, value in test_data.items():
        if field in fields:
            print(f"  {field}: {value}")
    
    # Initialize WebDriver
    print("\n🚀 Starting browser...")
    options = webdriver.ChromeOptions()
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    # Don't run headless so you can see the form
    
    driver = webdriver.Chrome(
        service=ChromeService(ChromeDriverManager().install()),
        options=options
    )
    
    try:
        # Navigate to form
        print(f"🌐 Opening form: {form_url}")
        driver.get(form_url)
        
        # Wait for page to load
        form_settings = config.get('form_settings', {})
        wait_after_load = form_settings.get('wait_after_load', 3)
        print(f"⏳ Waiting {wait_after_load} seconds for page to load...")
        time.sleep(wait_after_load)
        
        wait = WebDriverWait(driver, 10)
        
        print("\n📝 Filling form fields...")
        filled_fields = 0
        
        # Fill each field
        for field_name in fields.keys():
            if field_name in test_data:
                value = test_data[field_name]
                
                try:
                    print(f"  📝 Filling '{field_name}' with '{value}'...")
                    
                    # Find element using config manager
                    element = config_manager.find_element_with_fallbacks(driver, field_name)
                    
                    if element:
                        # Clear field
                        element.clear()
                        
                        # Copy to clipboard and paste (as per requirements)
                        pyperclip.copy(str(value))
                        element.send_keys(Keys.CONTROL, 'v')
                        
                        # Wait between fields
                        wait_time = form_settings.get('wait_between_fields', 0.5)
                        time.sleep(random.uniform(wait_time, wait_time + 0.3))
                        
                        filled_fields += 1
                        print(f"    ✅ Success")
                        
                    else:
                        print(f"    ❌ Could not find element for '{field_name}'")
                        
                except Exception as e:
                    print(f"    ❌ Error filling '{field_name}': {e}")
        
        print(f"\n📊 Filled {filled_fields}/{len([f for f in fields.keys() if f in test_data])} fields")
        
        # Verification phase
        print("\n🔍 Verifying filled data...")
        verification_results = {}
        
        for field_name in fields.keys():
            if field_name in test_data:
                try:
                    element = config_manager.find_element_with_fallbacks(driver, field_name)
                    if element:
                        entered_value = element.get_attribute('value')
                        expected_value = str(test_data[field_name])
                        
                        if entered_value == expected_value:
                            verification_results[field_name] = "✅ MATCH"
                            print(f"  ✅ {field_name}: '{entered_value}' ✓")
                        else:
                            verification_results[field_name] = f"❌ MISMATCH: Expected '{expected_value}', Got '{entered_value}'"
                            print(f"  ❌ {field_name}: Expected '{expected_value}', Got '{entered_value}'")
                    else:
                        verification_results[field_name] = "❌ ELEMENT NOT FOUND"
                        
                except Exception as e:
                    verification_results[field_name] = f"❌ ERROR: {e}"
                    print(f"  ❌ {field_name}: Error - {e}")
        
        # Summary
        matches = sum(1 for result in verification_results.values() if result == "✅ MATCH")
        total = len(verification_results)
        
        print(f"\n📊 VERIFICATION SUMMARY:")
        print(f"  ✅ Matches: {matches}/{total}")
        print(f"  📈 Accuracy: {(matches/total)*100:.1f}%")
        
        if matches == total:
            print("  🎉 ALL DATA PASTED CORRECTLY!")
        else:
            print("  ⚠️  Some fields have issues - check above")
        
        # Pause for manual inspection
        print("\n" + "="*60)
        print("🔍 MANUAL VERIFICATION TIME")
        print("="*60)
        print("The form is now filled with test data.")
        print("Please manually check each field to verify:")
        print("1. All data is pasted correctly")
        print("2. No fields are missing data")
        print("3. Data format looks correct")
        print("")
        print("If everything looks good:")
        print("- You can manually click 'Submit' to test submission")
        print("- Or press Enter here to close without submitting")
        print("")
        
        input("Press Enter when you're done checking (browser will close)...")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        
    finally:
        print("\n🔒 Closing browser...")
        driver.quit()
        print("✅ Test completed!")


if __name__ == "__main__":
    test_data_pasting()
