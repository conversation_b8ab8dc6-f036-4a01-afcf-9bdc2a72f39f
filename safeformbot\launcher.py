"""
SafeFormBot Launcher
Unified launcher for all SafeFormBot components
"""

import os
import sys
import argparse
import subprocess
import threading
import time
from pathlib import Path


def print_banner():
    """Print SafeFormBot banner"""
    banner = """
    ╔═══════════════════════════════════════════════════════════════╗
    ║                        SafeFormBot v2.0                       ║
    ║                    Enhanced Form Automation                   ║
    ╠═══════════════════════════════════════════════════════════════╣
    ║  🤖 Automated form submission with validation                ║
    ║  📊 Real-time analytics and performance monitoring           ║
    ║  ✅ Manual confirmation workflow                             ║
    ║  🔧 Advanced configuration management                        ║
    ║  🌐 Modern web interface                                     ║
    ╚═══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_requirements():
    """Check if required files exist"""
    required_files = [
        'main.py',
        'config_manager.py',
        'submission_manager.py',
        'analytics_dashboard.py',
        'xpath_generator.py',
        'web_interface.py',
        'form_config_enhanced.json'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ All required files found")
    return True


def install_requirements():
    """Install required Python packages"""
    requirements = [
        'pandas',
        'selenium',
        'webdriver-manager',
        'tqdm',
        'pyperclip',
        'flask',
        'flask-socketio',
        'matplotlib',
        'seaborn'
    ]
    
    print("📦 Installing required packages...")
    for package in requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package], 
                                stdout=subprocess.DEVNULL, 
                                stderr=subprocess.DEVNULL)
            print(f"   ✅ {package}")
        except subprocess.CalledProcessError:
            print(f"   ❌ Failed to install {package}")
            return False
    
    return True


def create_sample_data():
    """Create sample input.csv if it doesn't exist"""
    if not os.path.exists('input.csv'):
        print("📄 Creating sample input.csv...")
        
        sample_data = """ID NO.,First Name,Middle Initial,Last Name,Father's Name,Mother's Name,Mother's Maiden Name,Age,Weight in Kgs.,Age in Company (Years),% Hike Salary,Register joining code,Place Name,County,City,Region,FormURL
123456,John,A,Doe,Robert Doe,Mary Doe,Smith,25.5,70,2.5,15%,123-45-6789,Springfield,Clark,Springfield,Midwest,https://example.com/form
789012,Jane,B,Smith,William Smith,Susan Smith,Johnson,30.2,65,5.0,20%,***********,Riverside,Orange,Riverside,West,https://example.com/form"""
        
        with open('input.csv', 'w') as f:
            f.write(sample_data)
        
        print("   ✅ Sample input.csv created")


def run_web_interface():
    """Run the web interface"""
    print("🌐 Starting web interface...")
    try:
        subprocess.run([sys.executable, 'web_interface.py'], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Web interface stopped")
    except Exception as e:
        print(f"❌ Error running web interface: {e}")


def run_submission_gui():
    """Run the submission management GUI"""
    print("📋 Starting submission management GUI...")
    try:
        from submission_manager import start_submission_gui
        start_submission_gui()
    except KeyboardInterrupt:
        print("\n🛑 Submission GUI stopped")
    except Exception as e:
        print(f"❌ Error running submission GUI: {e}")


def run_analytics_dashboard():
    """Run the analytics dashboard"""
    print("📊 Starting analytics dashboard...")
    try:
        from analytics_dashboard import start_analytics_dashboard
        start_analytics_dashboard()
    except KeyboardInterrupt:
        print("\n🛑 Analytics dashboard stopped")
    except Exception as e:
        print(f"❌ Error running analytics dashboard: {e}")


def run_xpath_generator():
    """Run the XPath generator tool"""
    print("🔧 Starting XPath generator...")
    try:
        from xpath_generator import XPathGenerator
        
        form_url = input("Enter form URL: ").strip()
        if not form_url:
            print("❌ No URL provided")
            return
        
        generator = XPathGenerator()
        generator.start_browser(form_url, headless=False)
        
        try:
            generator.interactive_mode()
        finally:
            generator.close_browser()
            
    except KeyboardInterrupt:
        print("\n🛑 XPath generator stopped")
    except Exception as e:
        print(f"❌ Error running XPath generator: {e}")


def run_main_bot():
    """Run the main form submission bot"""
    print("🤖 Starting SafeFormBot...")
    try:
        subprocess.run([sys.executable, 'main.py'] + sys.argv[2:], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped")
    except Exception as e:
        print(f"❌ Error running bot: {e}")


def validate_configuration():
    """Validate form configuration"""
    print("🔍 Validating configuration...")
    try:
        from config_manager import ConfigManager
        
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        form_url = config.get('form_url')
        if not form_url:
            print("❌ No form URL in configuration")
            return False
        
        print(f"   Form URL: {form_url}")
        
        # Validate selectors
        results = config_manager.validate_selectors(form_url, headless=True)
        report = config_manager.generate_validation_report()
        
        print(report)
        
        # Save validation results
        with open('validation_report.txt', 'w') as f:
            f.write(report)
        
        print("   📄 Validation report saved to validation_report.txt")
        
        return True
        
    except Exception as e:
        print(f"❌ Error validating configuration: {e}")
        return False


def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(description="SafeFormBot Launcher")
    parser.add_argument('command', choices=[
        'web', 'gui', 'analytics', 'xpath', 'bot', 'validate', 'setup'
    ], help='Component to launch')
    
    args, unknown = parser.parse_known_args()
    
    print_banner()
    
    if args.command == 'setup':
        print("🔧 Setting up SafeFormBot...")
        
        if not check_requirements():
            print("❌ Setup failed - missing files")
            return
        
        if not install_requirements():
            print("❌ Setup failed - package installation")
            return
        
        create_sample_data()
        
        print("\n✅ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit form_config_enhanced.json with your form details")
        print("2. Update input.csv with your data")
        print("3. Run: python launcher.py validate")
        print("4. Run: python launcher.py web")
        
        return
    
    # Check requirements for all other commands
    if not check_requirements():
        print("❌ Please run 'python launcher.py setup' first")
        return
    
    if args.command == 'web':
        run_web_interface()
    elif args.command == 'gui':
        run_submission_gui()
    elif args.command == 'analytics':
        run_analytics_dashboard()
    elif args.command == 'xpath':
        run_xpath_generator()
    elif args.command == 'bot':
        run_main_bot()
    elif args.command == 'validate':
        validate_configuration()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 SafeFormBot launcher stopped")
    except Exception as e:
        print(f"\n❌ Launcher error: {e}")
        sys.exit(1)
