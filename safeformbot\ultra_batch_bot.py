"""
Ultra Batch Bot - Process 50-100 entries at once with minimal interruption
Designed to fill 2000 forms efficiently
"""

import pandas as pd
import time
import random
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import Chrome<PERSON>riverManager
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import pyperclip
from selenium.webdriver.common.keys import Keys
from config_manager import ConfigManager


class UltraBatchBot:
    """Ultra-fast batch processing for 2000 forms"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.config = None
        self.driver = None
        self.progress_file = "ultra_progress.json"
        self.batch_log_file = "ultra_batch_log.csv"
        
    def get_current_progress(self):
        """Get current progress from database"""
        submitted_count = 0
        last_entry = 0
        
        # Count from batch log
        if os.path.exists(self.batch_log_file):
            try:
                df = pd.read_csv(self.batch_log_file)
                submitted_count = len(df[df['Status'] == 'submitted'])
                if len(df) > 0:
                    # Get the highest entry number processed
                    last_entry = df['Entry_Number'].max()
            except:
                pass
        
        # You said you've submitted 8 entries (including Fermin)
        # So we start from entry 9
        if submitted_count == 0:
            submitted_count = 8  # Your confirmed submissions
            last_entry = 8
        
        return submitted_count, last_entry
    
    def save_batch_results(self, results):
        """Save batch results to database"""
        if not results:
            return
        
        df = pd.DataFrame(results)
        
        if os.path.exists(self.batch_log_file):
            df.to_csv(self.batch_log_file, mode='a', header=False, index=False)
        else:
            df.to_csv(self.batch_log_file, mode='w', header=True, index=False)
    
    def fill_single_form(self, row_data, entry_number):
        """Fill form for one person - optimized for speed"""
        try:
            # Navigate to form
            form_url = self.config.get('form_url')
            self.driver.get(form_url)
            time.sleep(2)  # Reduced wait time
            
            # Fill all fields quickly
            filled_count = 0
            for field_name in self.config.get('fields', {}).keys():
                if field_name in row_data:
                    value = str(row_data[field_name])
                    try:
                        element = self.config_manager.find_element_with_fallbacks(self.driver, field_name)
                        if element:
                            element.clear()
                            pyperclip.copy(value)
                            element.send_keys(Keys.CONTROL, 'v')
                            time.sleep(0.2)  # Faster typing
                            filled_count += 1
                    except:
                        continue
            
            return filled_count == 16
            
        except Exception as e:
            print(f"❌ Error filling form for entry {entry_number}: {e}")
            return False
    
    def process_batch(self, start_entry, batch_size=50):
        """Process a batch of entries"""
        print(f"\n🚀 ULTRA BATCH PROCESSING")
        print(f"📊 Processing entries {start_entry} to {start_entry + batch_size - 1}")
        print(f"🎯 Batch size: {batch_size} entries")
        print("=" * 60)
        
        # Load data
        df = pd.read_csv('input.csv')
        
        # Initialize browser
        print("🚀 Starting browser...")
        options = webdriver.ChromeOptions()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-logging')
        options.add_argument('--disable-gpu-logging')
        
        self.driver = webdriver.Chrome(
            service=ChromeService(ChromeDriverManager().install()),
            options=options
        )
        
        batch_results = []
        
        try:
            for i in range(batch_size):
                entry_number = start_entry + i
                
                if entry_number > len(df):
                    break
                
                row = df.iloc[entry_number - 1]  # Convert to 0-based index
                
                print(f"\n📝 [{i+1}/{batch_size}] Filling: {row['First Name']} {row['Last Name']} (ID: {row['ID NO.']})")
                
                # Fill form
                success = self.fill_single_form(dict(row), entry_number)
                
                if success:
                    print(f"✅ Form filled successfully - CHECK BROWSER AND SUBMIT")
                    
                    # Record for batch confirmation
                    batch_results.append({
                        'Entry_Number': entry_number,
                        'ID_NO': row['ID NO.'],
                        'First_Name': row['First Name'],
                        'Last_Name': row['Last Name'],
                        'Status': 'filled',  # Will be updated after batch confirmation
                        'Timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    })
                    
                    # Small delay between forms
                    time.sleep(1)
                else:
                    print(f"❌ Failed to fill form")
                    batch_results.append({
                        'Entry_Number': entry_number,
                        'ID_NO': row['ID NO.'],
                        'First_Name': row['First Name'],
                        'Last_Name': row['Last Name'],
                        'Status': 'error',
                        'Timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    })
            
            print(f"\n🎉 BATCH FILLING COMPLETE!")
            print(f"📊 Filled {len([r for r in batch_results if r['Status'] == 'filled'])} forms")
            print("=" * 60)
            
            # Show batch summary
            print("\n📋 BATCH SUMMARY:")
            for result in batch_results:
                if result['Status'] == 'filled':
                    print(f"✅ Entry {result['Entry_Number']}: {result['First_Name']} {result['Last_Name']}")
                else:
                    print(f"❌ Entry {result['Entry_Number']}: {result['First_Name']} {result['Last_Name']} - ERROR")
            
            # Get batch confirmation
            print(f"\n{'='*60}")
            print("🔍 BATCH CONFIRMATION")
            print(f"{'='*60}")
            print("Please check all the forms in your browser and submit them.")
            print("After submitting all forms, provide the status for each entry:")
            print("Format: Enter comma-separated status (S=Submitted, F=Failed, R=Retry)")
            print("Example: S,S,F,S,S (for 5 entries)")
            print(f"{'='*60}")
            
            # Get confirmation for each entry
            confirmed_results = []
            for i, result in enumerate(batch_results):
                if result['Status'] == 'filled':
                    while True:
                        response = input(f"Entry {result['Entry_Number']} ({result['First_Name']} {result['Last_Name']}): (S)ubmitted/(F)ailed/(R)etry: ").strip().upper()
                        
                        if response in ['S', 'SUBMITTED']:
                            result['Status'] = 'submitted'
                            print(f"✅ Marked as submitted")
                            break
                        elif response in ['F', 'FAILED']:
                            result['Status'] = 'failed'
                            print(f"❌ Marked as failed")
                            break
                        elif response in ['R', 'RETRY']:
                            result['Status'] = 'retry'
                            print(f"🔄 Marked for retry")
                            break
                        else:
                            print("Please enter S, F, or R")
                
                confirmed_results.append(result)
            
            # Save results
            self.save_batch_results(confirmed_results)
            
            # Show final stats
            submitted = len([r for r in confirmed_results if r['Status'] == 'submitted'])
            failed = len([r for r in confirmed_results if r['Status'] == 'failed'])
            retry = len([r for r in confirmed_results if r['Status'] == 'retry'])
            
            print(f"\n📊 BATCH RESULTS:")
            print(f"✅ Submitted: {submitted}")
            print(f"❌ Failed: {failed}")
            print(f"🔄 Retry: {retry}")
            
            return confirmed_results
            
        finally:
            print("\n🔒 Closing browser...")
            self.driver.quit()
    
    def run_ultra_batch(self, batch_size=50):
        """Run ultra batch processing"""
        print("🚀 ULTRA BATCH BOT - 2000 FORMS MISSION")
        print("=" * 50)
        
        # Load configuration
        self.config = self.config_manager.load_config()
        if not self.config:
            print("❌ Failed to load configuration")
            return
        
        # Get current progress
        submitted_count, last_entry = self.get_current_progress()
        
        print(f"📊 Current Progress:")
        print(f"  ✅ Already submitted: {submitted_count} forms")
        print(f"  🎯 Remaining: {2000 - submitted_count} forms")
        print(f"  📍 Starting from entry: {last_entry + 1}")
        
        # Calculate batches needed
        remaining = 2000 - submitted_count
        batches_needed = (remaining + batch_size - 1) // batch_size
        
        print(f"\n📋 Batch Plan:")
        print(f"  📦 Batch size: {batch_size} entries")
        print(f"  🔢 Batches needed: {batches_needed}")
        print(f"  ⏱️  Estimated time: {batches_needed * 10} minutes")
        
        # Confirm start
        response = input(f"\nStart processing batch of {batch_size} entries? (yes/no): ").strip().lower()
        if response not in ['yes', 'y']:
            print("❌ Cancelled")
            return
        
        # Process batch
        start_entry = last_entry + 1
        results = self.process_batch(start_entry, batch_size)
        
        return results


if __name__ == "__main__":
    bot = UltraBatchBot()
    
    print("🎯 ULTRA BATCH BOT - MISSION: 2000 FORMS")
    print("💡 This bot will process 50 entries at once")
    print("📝 You check and submit all forms, then confirm status")
    print("🚀 Much faster than one-by-one processing!")
    
    # Start with batch of 50
    bot.run_ultra_batch(batch_size=50)
