"""
Direct Communication System between User and Bot
Simple popup interface for user to communicate with the bot
"""

import tkinter as tk
from tkinter import messagebox, simpledialog
import json
import os
from datetime import datetime


class BotCommunication:
    """Simple communication interface between user and bot"""
    
    def __init__(self):
        self.communication_file = "bot_communication.json"
        self.root = None
    
    def show_submission_confirmation(self, person_name, id_no):
        """Show popup asking if user has submitted the form"""
        
        # Create root window if it doesn't exist
        if not self.root:
            self.root = tk.Tk()
            self.root.withdraw()  # Hide the main window
        
        # Show confirmation dialog
        title = "Form Submission Confirmation"
        message = f"""
Have you manually submitted the form for:

👤 Name: {person_name}
🆔 ID NO.: {id_no}

Click 'Yes' if you have submitted the form.
Click 'No' if you want to skip this entry.
Click 'Cancel' if you want to retry filling the form.
        """
        
        result = messagebox.askyesnocancel(title, message.strip())
        
        if result is True:
            return 'submitted'
        elif result is False:
            return 'skip'
        else:
            return 'retry'
    
    def show_error_notification(self, error_message):
        """Show error notification to user"""
        
        if not self.root:
            self.root = tk.Tk()
            self.root.withdraw()
        
        messagebox.showerror("Bot Error", f"Error occurred:\n\n{error_message}")
    
    def show_completion_notification(self, stats):
        """Show completion notification"""
        
        if not self.root:
            self.root = tk.Tk()
            self.root.withdraw()
        
        message = f"""
🎉 Processing Complete!

✅ Successfully submitted: {stats.get('submitted', 0)}
⏭️  Skipped: {stats.get('skipped', 0)}
❌ Errors: {stats.get('errors', 0)}

Total processed: {stats.get('total', 0)}
        """
        
        messagebox.showinfo("Processing Complete", message.strip())
    
    def ask_field_correction(self, field_name, current_value, person_name):
        """Ask user to provide correct value for a field"""
        
        if not self.root:
            self.root = tk.Tk()
            self.root.withdraw()
        
        title = f"Correct {field_name}"
        prompt = f"""
Person: {person_name}
Field: {field_name}
Current Value: {current_value}

Enter the correct value:
        """
        
        corrected_value = simpledialog.askstring(title, prompt.strip())
        return corrected_value
    
    def show_field_override_options(self):
        """Show options for field overrides"""
        
        if not self.root:
            self.root = tk.Tk()
            self.root.withdraw()
        
        # Create a simple dialog for field overrides
        override_window = tk.Toplevel(self.root)
        override_window.title("Field Override Settings")
        override_window.geometry("400x300")
        override_window.grab_set()  # Make it modal
        
        # Instructions
        tk.Label(override_window, text="Set constant values for specific fields:", 
                font=('Arial', 12, 'bold')).pack(pady=10)
        
        # City override
        city_frame = tk.Frame(override_window)
        city_frame.pack(pady=5, padx=20, fill='x')
        
        tk.Label(city_frame, text="City (constant for all entries):").pack(anchor='w')
        city_var = tk.StringVar(value="Rockhill Furnace")
        city_entry = tk.Entry(city_frame, textvariable=city_var, width=30)
        city_entry.pack(anchor='w')
        
        # Add more override fields as needed
        
        # Buttons
        button_frame = tk.Frame(override_window)
        button_frame.pack(pady=20)
        
        result = {'cancelled': True}
        
        def save_overrides():
            result['cancelled'] = False
            result['city'] = city_var.get()
            override_window.destroy()
        
        def cancel():
            override_window.destroy()
        
        tk.Button(button_frame, text="Save Overrides", command=save_overrides, 
                 bg='green', fg='white', padx=20).pack(side='left', padx=5)
        tk.Button(button_frame, text="Cancel", command=cancel, 
                 padx=20).pack(side='left', padx=5)
        
        # Wait for window to close
        override_window.wait_window()
        
        return result if not result.get('cancelled') else None
    
    def cleanup(self):
        """Clean up resources"""
        if self.root:
            self.root.destroy()


# Test the communication system
if __name__ == "__main__":
    comm = BotCommunication()
    
    # Test submission confirmation
    result = comm.show_submission_confirmation("Numbers E. Dominique", "254619")
    print(f"User response: {result}")
    
    # Test field override options
    overrides = comm.show_field_override_options()
    print(f"Field overrides: {overrides}")
    
    comm.cleanup()
