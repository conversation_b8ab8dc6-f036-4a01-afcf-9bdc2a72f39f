import pandas as pd
import time
import random
import json
import os
import argparse
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON><PERSON>
from selenium.webdriver.support import expected_conditions as EC
from tqdm import tqdm
import pyperclip
from selenium.webdriver.common.keys import Keys

# --- Constants ---
# File paths for the various CSV files used by the bot.
INPUT_CSV_FILE = "input.csv"  # The source data for the forms.
FORM_CONFIG_FILE = "form_config.json"  # Contains the form URL and field selectors.
SUBMITTED_CSV_FILE = "submitted.csv"  # A simple log of successfully submitted names.
ERRORS_CSV_FILE = "errors.csv"  # A log of rows that failed to process.
SUBMISSION_LOG_FILE = "submission_log.csv"  # A detailed log of all data submitted.

# --- Helper Functions ---
def load_form_config(config_file):
    """Loads the form configuration from the specified JSON file."""
    try:
        with open(config_file, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: '{config_file}' not found. Please create it.")
        return None
    except json.JSONDecodeError:
        print(f"Error: Could not decode '{config_file}'. Check for syntax errors.")
        return None

def load_input_data(csv_file):
    """Loads the input data from the specified CSV file."""
    try:
        return pd.read_csv(csv_file)
    except FileNotFoundError:
        print(f"Error: '{csv_file}' not found. Please create it.")
        return None

def initialize_webdriver():
    """Initializes and returns the Selenium WebDriver."""
    print("Initializing WebDriver...")
    options = webdriver.ChromeOptions()
    options.add_argument(f"user-data-dir={os.path.join(os.getcwd(), 'chrome_profile')}")
    service = ChromeService(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    print("WebDriver Initialized.")
    return driver

# --- Main Script ---

def main():
    """Main function to run the form submission bot."""
    # --- Argument Parsing ---
    parser = argparse.ArgumentParser(description="SafeFormBot: Automate Google Form submissions.")
    parser.add_argument('--start', type=int, default=1, help='The starting row number (1-based) from the CSV to process.')
    parser.add_argument('--end', type=int, default=None, help='The ending row number from the CSV to process.')
    args = parser.parse_args()

    print("--- SafeFormBot Initialized ---")

    # --- Load Configuration and Data ---
    form_config = load_form_config(FORM_CONFIG_FILE)
    if not form_config:
        return

    df = load_input_data(INPUT_CSV_FILE)
    if df is None:
        return

    form_url = form_config.get("form_url")
    fields = form_config.get("fields", {})
    submit_button_selector = form_config.get("submit_button")
    if not all([form_url, fields, submit_button_selector]):
        print("Error: form_config.json is missing required keys ('form_url', 'fields', 'submit_button').")
        return

    # --- Slice DataFrame based on args ---
    start_index = args.start - 1
    end_index = args.end if args.end is not None else len(df)
    df_to_process = df.iloc[start_index:end_index]
    print(f"Processing rows from {args.start} to {end_index}...")

    # --- Load already submitted names to avoid duplicates ---
    submitted_names = []
    if os.path.exists(SUBMITTED_CSV_FILE):
        try:
            submitted_df = pd.read_csv(SUBMITTED_CSV_FILE, header=None, names=['Name', 'Status', 'Timestamp'])
            if not submitted_df.empty:
                submitted_names = submitted_df['Name'].tolist()
        except pd.errors.EmptyDataError:
            pass # Ignore empty submitted.csv
    print(f"Found {len(submitted_names)} previously submitted entries to skip.")

    # --- Initialize WebDriver ---
    driver = initialize_webdriver()

    # --- Main Processing Loop ---
    # Iterate over each row in the DataFrame with a progress bar.
    for index, row in tqdm(df_to_process.iterrows(), total=len(df_to_process), desc="Submitting Forms"):
        # --- Skip already submitted ---
        if row['First Name'] in submitted_names:
            continue

        # --- Retry Logic ---
        # Attempt to process each row up to 3 times.
        retries = 3
        for attempt in range(retries):
            try:
                # --- Form Filling ---
                driver.get(form_url)
                wait = WebDriverWait(driver, 10) # Wait up to 10 seconds

                for field, css_selector in fields.items():
                    if field in row:
                        value = row[field]
                        element = wait.until(
                            EC.element_to_be_clickable((By.XPATH, css_selector))
                        )
                        pyperclip.copy(str(value))
                        element.send_keys(Keys.CONTROL, 'v')
                        time.sleep(random.uniform(0.3, 0.8))

                # --- Pre-Submission Verification ---
                all_match = True
                for field, css_selector in fields.items():
                    if field in row:
                        element = driver.find_element(By.XPATH, css_selector)
                        entered_value = element.get_attribute('value')
                        expected_value = str(row[field])
                        if entered_value != expected_value:
                            all_match = False
                            print(f"  -! MISMATCH on field '{field}': Expected '{expected_value}', but found '{entered_value}'")
                            break
                
                if all_match:
                    # --- Submit and Verify ---
                    submit_button = wait.until(
                        EC.element_to_be_clickable((By.XPATH, submit_button_selector))
                    )
                    submit_button.click()
                    time.sleep(random.uniform(2, 4))
                else:
                    raise Exception("Data verification failed. Halting submission for this row.")

                # --- Logging ---
                timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                
                # Log to the simple submitted.csv for quick checks
                with open(SUBMITTED_CSV_FILE, 'a', newline='') as f:
                    f.write(f'"{row["First Name"]}",Success,{timestamp}\n')

                # Log the full data to the detailed submission_log.csv
                log_df = pd.DataFrame([row])
                log_df['Timestamp'] = timestamp
                log_df.to_csv(SUBMISSION_LOG_FILE, mode='a', header=not os.path.exists(SUBMISSION_LOG_FILE), index=False)

                # --- Add delay between submissions ---
                delay = random.uniform(6, 13)
                time.sleep(delay)

                break # Exit the retry loop if successful

            except Exception as e:
                if attempt < retries - 1:
                    print(f"  -! ATTEMPT {attempt + 1} FAILED: {e}. Retrying...")
                    time.sleep(5) # Wait 5 seconds before retrying
                else:
                    print(f"  -! ALL ATTEMPTS FAILED for row {index + 1}: {e}")
                    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                    error_message = str(e).replace('"', '""').split('\n')[0]
                    with open(ERRORS_CSV_FILE, 'a', newline='') as f:
                        f.write(f'"{row["First Name"]}",""{error_message}"",{timestamp}\n')

    driver.quit()
    print("\n--- SafeFormBot Finished ---")

if __name__ == "__main__":
    main()
