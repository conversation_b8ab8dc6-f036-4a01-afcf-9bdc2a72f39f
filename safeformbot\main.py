import pandas as pd
import time
import random
import json
import os
import argparse
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON><PERSON>
from selenium.webdriver.support import expected_conditions as EC
from tqdm import tqdm
import pyperclip
from selenium.webdriver.common.keys import Keys
from config_manager import ConfigManager

# --- Constants ---
# File paths for the various CSV files used by the bot.
INPUT_CSV_FILE = "input.csv"  # The source data for the forms.
FORM_CONFIG_FILE = "form_config_enhanced.json"  # Enhanced configuration with validation
SUBMITTED_CSV_FILE = "submitted.csv"  # A simple log of successfully submitted names.
ERRORS_CSV_FILE = "errors.csv"  # A log of rows that failed to process.
SUBMISSION_LOG_FILE = "submission_log.csv"  # A detailed log of all data submitted.
PENDING_SUBMISSIONS_FILE = "pending_submissions.csv"  # Submissions awaiting confirmation
VALIDATION_REPORT_FILE = "validation_report.json"  # Selector validation results

# --- Helper Functions ---
def load_form_config(config_file):
    """Loads the enhanced form configuration from the specified JSON file."""
    try:
        config_manager = ConfigManager(config_file)
        config = config_manager.load_config()

        # Validate configuration structure
        required_keys = ['form_url', 'fields']
        for key in required_keys:
            if key not in config:
                print(f"Error: Missing required key '{key}' in configuration.")
                return None

        return config, config_manager
    except FileNotFoundError:
        print(f"Error: '{config_file}' not found. Please create it.")
        return None, None
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return None, None

def load_input_data(csv_file):
    """Loads the input data from the specified CSV file."""
    try:
        return pd.read_csv(csv_file)
    except FileNotFoundError:
        print(f"Error: '{csv_file}' not found. Please create it.")
        return None

def initialize_webdriver():
    """Initializes and returns the Selenium WebDriver."""
    print("Initializing WebDriver...")
    options = webdriver.ChromeOptions()
    options.add_argument(f"user-data-dir={os.path.join(os.getcwd(), 'chrome_profile')}")
    service = ChromeService(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    print("WebDriver Initialized.")
    return driver

def fill_field_with_validation(driver, config_manager, field_name, value, wait):
    """Fill a field using enhanced configuration with fallback selectors and validation."""
    # Validate data before filling
    is_valid, validation_message = config_manager.validate_field_data(field_name, str(value))
    if not is_valid:
        raise ValueError(f"Data validation failed for {field_name}: {validation_message}")

    # Find element using primary and fallback selectors
    element = config_manager.find_element_with_fallbacks(driver, field_name)
    if not element:
        raise Exception(f"Could not find element for field '{field_name}' using any selector")

    # Clear field and fill with data
    element.clear()
    pyperclip.copy(str(value))
    element.send_keys(Keys.CONTROL, 'v')

    # Wait a bit for the field to update
    form_settings = config_manager.config.get('form_settings', {})
    wait_time = form_settings.get('wait_between_fields', 0.5)
    time.sleep(random.uniform(wait_time, wait_time + 0.3))

    # Verify the field was filled correctly if validation is enabled
    if form_settings.get('validate_each_field', True):
        entered_value = element.get_attribute('value')
        expected_value = str(value)
        if entered_value != expected_value:
            raise Exception(f"Field validation failed for '{field_name}': Expected '{expected_value}', got '{entered_value}'")

    return True

def log_pending_submission(row, timestamp):
    """Log a submission as pending confirmation."""
    pending_data = {
        'ID NO.': row.get('ID NO.', ''),
        'First Name': row.get('First Name', ''),
        'Last Name': row.get('Last Name', ''),
        'Timestamp': timestamp,
        'Status': 'PENDING_CONFIRMATION'
    }

    # Create or append to pending submissions file
    df_pending = pd.DataFrame([pending_data])
    if os.path.exists(PENDING_SUBMISSIONS_FILE):
        df_pending.to_csv(PENDING_SUBMISSIONS_FILE, mode='a', header=False, index=False)
    else:
        df_pending.to_csv(PENDING_SUBMISSIONS_FILE, mode='w', header=True, index=False)

# --- Main Script ---

def main():
    """Main function to run the form submission bot."""
    # --- Argument Parsing ---
    parser = argparse.ArgumentParser(description="SafeFormBot: Automate Google Form submissions.")
    parser.add_argument('--start', type=int, default=1, help='The starting row number (1-based) from the CSV to process.')
    parser.add_argument('--end', type=int, default=None, help='The ending row number from the CSV to process.')
    args = parser.parse_args()

    print("--- SafeFormBot Initialized ---")

    # --- Load Enhanced Configuration and Data ---
    config_result = load_form_config(FORM_CONFIG_FILE)
    if not config_result or config_result[0] is None:
        return

    form_config, config_manager = config_result

    df = load_input_data(INPUT_CSV_FILE)
    if df is None:
        return

    form_url = form_config.get("form_url")
    fields = form_config.get("fields", {})
    submit_button_config = form_config.get("submit_button", {})

    if not all([form_url, fields]):
        print("Error: Configuration is missing required keys ('form_url', 'fields').")
        return

    # Validate selectors before starting (optional)
    validation_settings = form_config.get('validation_settings', {})
    if validation_settings.get('validate_before_run', True):
        print("Validating form selectors...")
        validation_results = config_manager.validate_selectors(form_url, headless=True)

        # Save validation results
        with open(VALIDATION_REPORT_FILE, 'w') as f:
            import json
            json.dump(validation_results, f, indent=2)

        # Check if validation passed
        summary = validation_results.get('summary', {})
        if summary.get('invalid', 0) > 0:
            print(f"⚠️  Warning: {summary.get('invalid', 0)} selectors failed validation")
            print("Check validation_report.json for details")

            response = input("Continue anyway? (y/N): ").strip().lower()
            if response != 'y':
                print("Aborting due to validation failures")
                return
        else:
            print("✅ All selectors validated successfully")

    # --- Slice DataFrame based on args ---
    start_index = args.start - 1
    end_index = args.end if args.end is not None else len(df)
    df_to_process = df.iloc[start_index:end_index]
    print(f"Processing rows from {args.start} to {end_index}...")

    # --- Load already submitted names to avoid duplicates ---
    submitted_names = []
    if os.path.exists(SUBMITTED_CSV_FILE):
        try:
            submitted_df = pd.read_csv(SUBMITTED_CSV_FILE, header=None, names=['Name', 'Status', 'Timestamp'])
            if not submitted_df.empty:
                submitted_names = submitted_df['Name'].tolist()
        except pd.errors.EmptyDataError:
            pass # Ignore empty submitted.csv
    print(f"Found {len(submitted_names)} previously submitted entries to skip.")

    # --- Initialize WebDriver ---
    driver = initialize_webdriver()

    # --- Main Processing Loop ---
    # Iterate over each row in the DataFrame with a progress bar.
    for index, row in tqdm(df_to_process.iterrows(), total=len(df_to_process), desc="Submitting Forms"):
        # --- Skip already submitted ---
        if row['First Name'] in submitted_names:
            continue

        # --- Retry Logic ---
        # Attempt to process each row up to 3 times.
        retries = 3
        for attempt in range(retries):
            try:
                # --- Enhanced Form Filling ---
                driver.get(form_url)

                # Wait for page to load
                form_settings = form_config.get('form_settings', {})
                wait_after_load = form_settings.get('wait_after_load', 3)
                time.sleep(wait_after_load)

                wait = WebDriverWait(driver, 10)

                # Fill fields using enhanced configuration
                for field_name in fields.keys():
                    if field_name in row:
                        value = row[field_name]
                        try:
                            fill_field_with_validation(driver, config_manager, field_name, value, wait)
                            print(f"  ✅ Filled '{field_name}': {value}")
                        except Exception as e:
                            print(f"  ❌ Failed to fill '{field_name}': {e}")
                            raise

                # --- Enhanced Pre-Submission Verification ---
                print("  🔍 Verifying all fields...")
                all_match = True
                verification_errors = []

                for field_name in fields.keys():
                    if field_name in row:
                        element = config_manager.find_element_with_fallbacks(driver, field_name)
                        if element:
                            entered_value = element.get_attribute('value')
                            expected_value = str(row[field_name])
                            if entered_value != expected_value:
                                all_match = False
                                error_msg = f"MISMATCH on field '{field_name}': Expected '{expected_value}', but found '{entered_value}'"
                                verification_errors.append(error_msg)
                                print(f"  -! {error_msg}")

                if all_match:
                    # --- Enhanced Submit Button Handling ---
                    print("  📤 Submitting form...")

                    # Find submit button using primary and fallback selectors
                    submit_element = None
                    submit_config = form_config.get('submit_button', {})

                    # Try primary selector
                    primary_selector = submit_config.get('primary_selector')
                    if primary_selector:
                        try:
                            submit_element = wait.until(EC.element_to_be_clickable((By.XPATH, primary_selector)))
                        except:
                            pass

                    # Try fallback selectors if primary failed
                    if not submit_element:
                        fallback_selectors = submit_config.get('fallback_selectors', [])
                        for selector in fallback_selectors:
                            try:
                                if selector.startswith('//') or selector.startswith('/'):
                                    submit_element = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                                else:
                                    submit_element = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                                break
                            except:
                                continue

                    if not submit_element:
                        raise Exception("Could not find submit button using any selector")

                    # Wait before submitting
                    wait_before_submit = form_settings.get('wait_before_submit', 2)
                    time.sleep(wait_before_submit)

                    # Log as pending submission before clicking submit
                    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                    log_pending_submission(row, timestamp)

                    submit_element.click()
                    time.sleep(random.uniform(2, 4))

                    print("  ✅ Form submitted successfully!")
                else:
                    error_summary = "; ".join(verification_errors)
                    raise Exception(f"Data verification failed: {error_summary}")

                # --- Logging ---
                timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                
                # Log to the simple submitted.csv for quick checks
                with open(SUBMITTED_CSV_FILE, 'a', newline='') as f:
                    f.write(f'"{row["First Name"]}",Success,{timestamp}\n')

                # Log the full data to the detailed submission_log.csv
                log_df = pd.DataFrame([row])
                log_df['Timestamp'] = timestamp
                log_df.to_csv(SUBMISSION_LOG_FILE, mode='a', header=not os.path.exists(SUBMISSION_LOG_FILE), index=False)

                # --- Add delay between submissions ---
                delay = random.uniform(6, 13)
                time.sleep(delay)

                break # Exit the retry loop if successful

            except Exception as e:
                if attempt < retries - 1:
                    print(f"  -! ATTEMPT {attempt + 1} FAILED: {e}. Retrying...")
                    time.sleep(5) # Wait 5 seconds before retrying
                else:
                    print(f"  -! ALL ATTEMPTS FAILED for row {index + 1}: {e}")
                    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                    error_message = str(e).replace('"', '""').split('\n')[0]
                    with open(ERRORS_CSV_FILE, 'a', newline='') as f:
                        f.write(f'"{row["First Name"]}",""{error_message}"",{timestamp}\n')

    driver.quit()
    print("\n--- SafeFormBot Finished ---")

if __name__ == "__main__":
    main()
