"""
Fix the % Hike Salary field selector
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
import time

def find_hike_salary_selector():
    """Find the correct selector for % Hike Salary field"""
    
    form_url = "https://docs.google.com/forms/d/e/1FAIpQLSfrorZVKQbYGWTBjpP7bgECZHRuZsGu9f0IP6P6QxHjHE8-sw/viewform"
    
    print("🔍 Finding correct selector for % Hike Salary field...")
    
    # Initialize browser
    options = webdriver.ChromeOptions()
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    
    driver = webdriver.Chrome(
        service=ChromeService(ChromeDriverManager().install()),
        options=options
    )
    
    try:
        driver.get(form_url)
        time.sleep(3)
        
        # Try different variations of the text
        variations = [
            "% Hike Salary",
            " % Hike Salary", 
            "%Hike Salary",
            "Hike Salary",
            "% Hike",
            "Salary"
        ]
        
        print("\n🔍 Testing different text variations:")
        
        for variation in variations:
            try:
                # Try to find elements containing this text
                xpath = f"//*[contains(text(), \"{variation}\")]"
                elements = driver.find_elements(By.XPATH, xpath)
                
                if elements:
                    print(f"✅ Found {len(elements)} elements with text '{variation}'")
                    
                    for i, elem in enumerate(elements):
                        try:
                            # Try to find input field in ancestor
                            input_xpath = f"//*[contains(text(), \"{variation}\")]/ancestor::div[contains(@class, 'geS5n')]//input"
                            input_elements = driver.find_elements(By.XPATH, input_xpath)
                            
                            if input_elements:
                                print(f"  ✅ Input field found with: {input_xpath}")
                                
                                # Test if we can interact with it
                                input_elem = input_elements[0]
                                if input_elem.is_displayed() and input_elem.is_enabled():
                                    print(f"  ✅ Field is interactive!")
                                    print(f"  📋 Recommended selector: {input_xpath}")
                                    return input_xpath
                                else:
                                    print(f"  ❌ Field not interactive")
                            else:
                                print(f"  ❌ No input field found")
                                
                        except Exception as e:
                            print(f"  ❌ Error testing input: {e}")
                else:
                    print(f"❌ No elements found with text '{variation}'")
                    
            except Exception as e:
                print(f"❌ Error testing '{variation}': {e}")
        
        # Try alternative approaches
        print("\n🔍 Trying alternative approaches:")
        
        # Look for all form fields and their labels
        try:
            form_elements = driver.find_elements(By.XPATH, "//div[contains(@class, 'geS5n')]")
            print(f"Found {len(form_elements)} form sections")
            
            for i, elem in enumerate(form_elements):
                try:
                    # Get all text in this section
                    text_content = elem.text.strip()
                    if text_content and ("hike" in text_content.lower() or "salary" in text_content.lower() or "%" in text_content):
                        print(f"  📍 Section {i}: '{text_content}'")
                        
                        # Check if it has an input
                        inputs = elem.find_elements(By.XPATH, ".//input")
                        if inputs:
                            print(f"    ✅ Has input field")
                            
                            # Generate XPath for this specific section
                            section_xpath = f"(//div[contains(@class, 'geS5n')])[{i+1}]//input"
                            print(f"    📋 Suggested XPath: {section_xpath}")
                            
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"❌ Error in alternative approach: {e}")
        
    finally:
        input("\nPress Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    find_hike_salary_selector()
