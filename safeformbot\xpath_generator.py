"""
XPath Generator Tool for SafeFormBot
Interactive tool for generating and testing XPath selectors
"""

import time
import json
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class XPathGenerator:
    """Interactive XPath generator and tester"""
    
    def __init__(self):
        self.driver = None
        self.form_url = None
        self.generated_selectors = {}
        
    def start_browser(self, form_url: str, headless: bool = False) -> None:
        """Start browser and navigate to form"""
        options = webdriver.ChromeOptions()
        if headless:
            options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        self.driver = webdriver.Chrome(
            service=ChromeService(ChromeDriverManager().install()),
            options=options
        )
        
        self.form_url = form_url
        self.driver.get(form_url)
        time.sleep(3)  # Wait for page to load
        
        print(f"Browser opened and navigated to: {form_url}")
        print("You can now inspect elements and generate XPaths")
    
    def close_browser(self) -> None:
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            print("Browser closed")
    
    def generate_xpath_for_text(self, text: str, element_type: str = "input") -> List[str]:
        """Generate XPath selectors for elements containing specific text"""
        xpaths = []
        
        # Basic text-based XPaths
        xpaths.extend([
            f"//*[contains(text(), \"{text}\")]/ancestor::div[contains(@class, 'geS5n')]//{element_type}",
            f"//*[contains(text(), \"{text}\")]/following-sibling::{element_type}",
            f"//*[contains(text(), \"{text}\")]/parent::*//{element_type}",
            f"//label[contains(text(), \"{text}\")]/following-sibling::{element_type}",
            f"//label[contains(text(), \"{text}\")]/..//{element_type}",
        ])
        
        # Attribute-based alternatives
        text_lower = text.lower().replace(" ", "_").replace("'", "")
        xpaths.extend([
            f"//{element_type}[contains(@aria-label, \"{text}\")]",
            f"//{element_type}[contains(@placeholder, \"{text}\")]",
            f"//{element_type}[contains(@name, \"{text_lower}\")]",
            f"//{element_type}[contains(@id, \"{text_lower}\")]",
        ])
        
        return xpaths
    
    def test_xpath(self, xpath: str) -> Dict:
        """Test an XPath selector and return results"""
        if not self.driver:
            return {"error": "Browser not started. Call start_browser() first."}
        
        result = {
            "xpath": xpath,
            "found": False,
            "count": 0,
            "elements": [],
            "error": None
        }
        
        try:
            elements = self.driver.find_elements(By.XPATH, xpath)
            result["found"] = len(elements) > 0
            result["count"] = len(elements)
            
            for i, element in enumerate(elements[:5]):  # Limit to first 5 elements
                element_info = {
                    "index": i,
                    "tag": element.tag_name,
                    "id": element.get_attribute('id') or '',
                    "class": element.get_attribute('class') or '',
                    "name": element.get_attribute('name') or '',
                    "type": element.get_attribute('type') or '',
                    "aria_label": element.get_attribute('aria-label') or '',
                    "placeholder": element.get_attribute('placeholder') or '',
                    "text": element.text[:50] if element.text else '',
                    "visible": element.is_displayed(),
                    "enabled": element.is_enabled()
                }
                result["elements"].append(element_info)
                
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def test_css_selector(self, css_selector: str) -> Dict:
        """Test a CSS selector and return results"""
        if not self.driver:
            return {"error": "Browser not started. Call start_browser() first."}
        
        result = {
            "css_selector": css_selector,
            "found": False,
            "count": 0,
            "elements": [],
            "error": None
        }
        
        try:
            elements = self.driver.find_elements(By.CSS_SELECTOR, css_selector)
            result["found"] = len(elements) > 0
            result["count"] = len(elements)
            
            for i, element in enumerate(elements[:5]):  # Limit to first 5 elements
                element_info = {
                    "index": i,
                    "tag": element.tag_name,
                    "id": element.get_attribute('id') or '',
                    "class": element.get_attribute('class') or '',
                    "name": element.get_attribute('name') or '',
                    "type": element.get_attribute('type') or '',
                    "aria_label": element.get_attribute('aria-label') or '',
                    "placeholder": element.get_attribute('placeholder') or '',
                    "text": element.text[:50] if element.text else '',
                    "visible": element.is_displayed(),
                    "enabled": element.is_enabled()
                }
                result["elements"].append(element_info)
                
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def find_best_selector(self, field_name: str, text_hints: List[str] = None) -> Dict:
        """Find the best selector for a field using multiple strategies"""
        if not self.driver:
            return {"error": "Browser not started. Call start_browser() first."}
        
        if not text_hints:
            text_hints = [field_name]
        
        results = {
            "field_name": field_name,
            "tested_selectors": [],
            "best_selector": None,
            "best_type": None,
            "alternatives": []
        }
        
        # Test XPath selectors
        for hint in text_hints:
            xpaths = self.generate_xpath_for_text(hint)
            for xpath in xpaths:
                test_result = self.test_xpath(xpath)
                test_result["selector_type"] = "xpath"
                results["tested_selectors"].append(test_result)
                
                # If we found exactly one visible, enabled element, this might be our best selector
                if (test_result["count"] == 1 and 
                    test_result["elements"] and 
                    test_result["elements"][0]["visible"] and 
                    test_result["elements"][0]["enabled"]):
                    if not results["best_selector"]:
                        results["best_selector"] = xpath
                        results["best_type"] = "xpath"
                    else:
                        results["alternatives"].append({"selector": xpath, "type": "xpath"})
        
        # Test CSS selectors
        css_selectors = self._generate_css_selectors(field_name, text_hints)
        for css_selector in css_selectors:
            test_result = self.test_css_selector(css_selector)
            test_result["selector_type"] = "css"
            results["tested_selectors"].append(test_result)
            
            # If we found exactly one visible, enabled element, this might be our best selector
            if (test_result["count"] == 1 and 
                test_result["elements"] and 
                test_result["elements"][0]["visible"] and 
                test_result["elements"][0]["enabled"]):
                if not results["best_selector"]:
                    results["best_selector"] = css_selector
                    results["best_type"] = "css"
                else:
                    results["alternatives"].append({"selector": css_selector, "type": "css"})
        
        return results
    
    def _generate_css_selectors(self, field_name: str, text_hints: List[str]) -> List[str]:
        """Generate CSS selectors for a field"""
        selectors = []
        
        for hint in text_hints:
            hint_lower = hint.lower().replace(" ", "_").replace("'", "")
            selectors.extend([
                f"input[aria-label*='{hint}']",
                f"input[placeholder*='{hint}']",
                f"input[name*='{hint_lower}']",
                f"input[id*='{hint_lower}']",
                f"#{hint_lower}",
                f".{hint_lower}",
                f"[name='{hint_lower}']",
                f"[id='{hint_lower}']"
            ])
        
        return selectors
    
    def interactive_mode(self) -> None:
        """Interactive mode for generating selectors"""
        print("\n=== XPath Generator Interactive Mode ===")
        print("Commands:")
        print("  'test <xpath_or_css>' - Test a selector")
        print("  'find <field_name>' - Find selectors for a field")
        print("  'generate <text>' - Generate XPaths for text")
        print("  'save' - Save current selectors to config")
        print("  'quit' - Exit interactive mode")
        print()
        
        while True:
            try:
                command = input("XPathGen> ").strip()
                
                if command.lower() == 'quit':
                    break
                elif command.startswith('test '):
                    selector = command[5:].strip()
                    if selector.startswith('//') or selector.startswith('/'):
                        result = self.test_xpath(selector)
                    else:
                        result = self.test_css_selector(selector)
                    self._print_test_result(result)
                    
                elif command.startswith('find '):
                    field_name = command[5:].strip()
                    result = self.find_best_selector(field_name)
                    self._print_find_result(result)
                    
                elif command.startswith('generate '):
                    text = command[9:].strip()
                    xpaths = self.generate_xpath_for_text(text)
                    print(f"Generated XPaths for '{text}':")
                    for i, xpath in enumerate(xpaths, 1):
                        print(f"  {i}. {xpath}")
                    
                elif command == 'save':
                    self._save_selectors()
                    
                else:
                    print("Unknown command. Type 'quit' to exit.")
                    
            except KeyboardInterrupt:
                print("\nExiting...")
                break
            except Exception as e:
                print(f"Error: {e}")
    
    def _print_test_result(self, result: Dict) -> None:
        """Print test result in a readable format"""
        selector = result.get('xpath') or result.get('css_selector')
        print(f"\nTesting: {selector}")
        
        if result.get('error'):
            print(f"❌ Error: {result['error']}")
            return
        
        if result['found']:
            print(f"✅ Found {result['count']} element(s)")
            for element in result['elements']:
                status = "👁️" if element['visible'] else "🙈"
                enabled = "✅" if element['enabled'] else "❌"
                print(f"  {status} {enabled} <{element['tag']}> id='{element['id']}' class='{element['class']}'")
        else:
            print("❌ No elements found")
    
    def _print_find_result(self, result: Dict) -> None:
        """Print find result in a readable format"""
        print(f"\nFinding selectors for: {result['field_name']}")
        
        if result['best_selector']:
            print(f"✅ Best selector ({result['best_type']}): {result['best_selector']}")
            
            if result['alternatives']:
                print("📋 Alternatives:")
                for alt in result['alternatives'][:3]:  # Show top 3 alternatives
                    print(f"  - ({alt['type']}) {alt['selector']}")
        else:
            print("❌ No suitable selector found")
            print("Tested selectors:")
            for test in result['tested_selectors'][:5]:  # Show first 5 tests
                selector = test.get('xpath') or test.get('css_selector')
                status = f"Found {test['count']}" if test['found'] else "Not found"
                print(f"  - {selector[:60]}... ({status})")
    
    def _save_selectors(self) -> None:
        """Save generated selectors to a file"""
        filename = f"generated_selectors_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump(self.generated_selectors, f, indent=2)
        print(f"Selectors saved to {filename}")


if __name__ == "__main__":
    # Example usage
    generator = XPathGenerator()
    
    # Start browser (you can set headless=False to see the browser)
    form_url = "https://docs.google.com/forms/d/e/1FAIpQLSewJFrDBXLIijIcUXOD5Uvx-5_SqgZEfAdomg_9G0DPVhUoag/viewform"
    generator.start_browser(form_url, headless=False)
    
    try:
        # Enter interactive mode
        generator.interactive_mode()
    finally:
        generator.close_browser()
