import pandas as pd
import argparse

# --- Constants ---
ORIGINAL_INPUT_FILE = 'input.csv'
RESPONSES_FILE = 'submission_log.csv'
UNIQUE_ID_COLUMN = 'ID NO.' # The column that uniquely identifies each record

# --- Helper Functions ---
def load_dataframes():
    """Loads the original and response data from their respective CSV files."""
    try:
        original_df = pd.read_csv(ORIGINAL_INPUT_FILE, dtype=str).fillna('')
        responses_df = pd.read_csv(RESPONSES_FILE, dtype=str).fillna('')
        return original_df, responses_df
    except FileNotFoundError as e:
        print(f"Error: Could not find a required file. Make sure both '{ORIGINAL_INPUT_FILE}' and '{RESPONSES_FILE}' are present.")
        print(f"File not found: {e.filename}")
        return None, None

def prepare_dataframes(original_df, responses_df):
    """Prepares the dataframes for comparison by cleaning and standardizing them."""
    if 'Timestamp' in responses_df.columns:
        responses_df = responses_df.drop(columns=['Timestamp'])
    if 'FormURL' in original_df.columns:
        original_df = original_df.drop(columns=['FormURL'])

    original_df.columns = original_df.columns.str.strip()
    responses_df.columns = responses_df.columns.str.strip()

    if UNIQUE_ID_COLUMN not in original_df.columns or UNIQUE_ID_COLUMN not in responses_df.columns:
        print(f"---! FATAL ERROR: Unique ID Column '{UNIQUE_ID_COLUMN}' not found in both files. !---")
        return None, None

    return original_df, responses_df

def run_validation(start_row, end_row):
    """Compares original data with submitted data using a unique ID, providing a robust accuracy report."""
    print("--- Accuracy Validator Initialized ---")

    # --- Load and Prepare Data ---
    original_df, responses_df = load_dataframes()
    if original_df is None or responses_df is None:
        return

    print(f"Loaded {len(original_df)} original records and {len(responses_df)} submitted responses.")

    original_df, responses_df = prepare_dataframes(original_df, responses_df)
    if original_df is None or responses_df is None:
        return

    # --- Slice the original dataframe to the specified test range ---
    if start_row is not None and end_row is not None:
        original_df_slice = original_df.iloc[start_row-1:end_row]
    else:
        original_df_slice = original_df

    print(f"Validating against {len(original_df_slice)} records from input.csv (rows {start_row or 1}-{end_row or len(original_df)})...")

    # --- Validation using Merge ---
    # Merge based on the unique ID to align records perfectly.
    comparison_df = pd.merge(
        original_df_slice,
        responses_df,
        on=UNIQUE_ID_COLUMN,
        suffixes=('_expected', '_actual'),
        how='left' # Keep all records from the original slice
    )

    # --- Calculate Accuracy ---
    total_records_checked = len(comparison_df)
    total_cells = 0
    mismatched_cells = 0
    error_details = []

    # Check for records that were in the input slice but not found in the responses
    unsubmitted_records = comparison_df[comparison_df.isnull().any(axis=1)]
    if not unsubmitted_records.empty:
        for index, row in unsubmitted_records.iterrows():
            record_id = row[UNIQUE_ID_COLUMN]
            error_details.append(f"  - Record NOT FOUND in responses.csv: ID NO. {record_id}")
            mismatched_cells += len(original_df_slice.columns) -1 # All cells are considered mismatched

    # Filter to only the records that were successfully submitted
    comparison_df.dropna(inplace=True)

    # Get common columns to compare (excluding the ID itself)
    common_columns = [col for col in original_df_slice.columns if col != UNIQUE_ID_COLUMN and f"{col}_actual" in comparison_df.columns]

    for index, row in comparison_df.iterrows():
        record_id = row[UNIQUE_ID_COLUMN]
        for col in common_columns:
            total_cells += 1
            val_expected = str(row[f"{col}_expected"])
            val_actual = str(row[f"{col}_actual"])

            if val_expected != val_actual:
                mismatched_cells += 1
                error_details.append(
                    f"  - Mismatch on Record ID: {record_id}\n" 
                    f"    - Field:    '{col}'\n"
                    f"    - Expected: '{val_expected}'\n"
                    f"    - Got:      '{val_actual}'"
                )

    print("--- Validation Complete ---")

    # --- Final Report ---
    accuracy = ((total_cells - mismatched_cells) / total_cells) * 100 if total_cells > 0 else 100

    print("\n--- ACCURACY REPORT ---")
    print(f"Total Records Checked: {total_records_checked}")
    print(f"Total Cells Checked:   {total_cells}")
    print(f"Mismatched Cells:    {mismatched_cells}")
    print(f"Overall Accuracy:      {accuracy:.2f}%")

    if error_details:
        print("\n--- Error Details ---")
        for detail in error_details:
            print(detail)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Validate Google Form submissions.")
    parser.add_argument('--start', type=int, help='The starting row number (1-based) from the input.csv to validate.')
    parser.add_argument('--end', type=int, help='The ending row number from the input.csv to validate.')
    args = parser.parse_args()
    
    run_validation(args.start, args.end)
