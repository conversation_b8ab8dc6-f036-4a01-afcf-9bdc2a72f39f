"""
Batch Submission Bot - Process multiple entries with simple terminal confirmation
No popups, just simple yes/no in terminal after each form fill
"""

import pandas as pd
import time
import random
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pyperclip
from selenium.webdriver.common.keys import Keys
from config_manager import ConfigManager


class BatchSubmissionBot:
    """Batch processing bot with simple terminal confirmation"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.config = None
        self.driver = None
        self.progress_file = "batch_progress.json"
        self.submitted_count = 0
        
    def load_progress(self):
        """Load progress from previous session"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r') as f:
                    progress = json.load(f)
                return progress.get('last_completed_row', 0)
            except:
                return 0
        return 0
    
    def save_progress(self, completed_row):
        """Save current progress"""
        progress = {
            'last_completed_row': completed_row,
            'timestamp': datetime.now().isoformat(),
            'submitted_count': self.submitted_count
        }
        with open(self.progress_file, 'w') as f:
            json.dump(progress, f, indent=2)
    
    def get_submitted_entries(self):
        """Get list of already submitted entries"""
        submitted_ids = set()
        
        # You said you've submitted 7 entries, so let's protect them
        # Based on your message: entries 1-7 are submitted, now working on 8th (Fermin)
        if os.path.exists('input.csv'):
            df = pd.read_csv('input.csv')
            for i in range(7):  # First 7 entries (indices 0-6)
                if i < len(df):
                    id_no = str(df.iloc[i]['ID NO.'])
                    name = f"{df.iloc[i]['First Name']} {df.iloc[i]['Last Name']}"
                    submitted_ids.add(id_no)
                    print(f"🛡️  Protected: {name} (ID: {id_no})")
        
        print(f"🛡️  Total protected: {len(submitted_ids)} entries")
        return submitted_ids
    
    def fill_form_fields(self, row_data):
        """Fill all form fields for one person"""
        print(f"\n📝 Filling form for: {row_data['First Name']} {row_data['Last Name']} (ID: {row_data['ID NO.']})")
        
        # Navigate to form
        form_url = self.config.get('form_url')
        self.driver.get(form_url)
        
        # Wait for form to load
        wait_time = self.config.get('form_settings', {}).get('wait_after_load', 3)
        time.sleep(wait_time)
        
        # Fill all fields
        filled_count = 0
        for field_name in self.config.get('fields', {}).keys():
            if field_name in row_data:
                value = row_data[field_name]
                try:
                    # Find element
                    element = self.config_manager.find_element_with_fallbacks(self.driver, field_name)
                    if element:
                        # Clear and fill
                        element.clear()
                        pyperclip.copy(str(value))
                        element.send_keys(Keys.CONTROL, 'v')
                        time.sleep(random.uniform(0.3, 0.7))
                        filled_count += 1
                        print(f"  ✅ {field_name}: {value}")
                    else:
                        print(f"  ❌ Could not find: {field_name}")
                except Exception as e:
                    print(f"  ❌ Error filling {field_name}: {e}")
        
        print(f"\n📊 Filled {filled_count}/16 fields")
        return filled_count == 16
    
    def verify_fields(self, row_data):
        """Verify all fields are filled correctly"""
        print("\n🔍 Verifying fields...")
        correct_count = 0
        total_count = 0
        
        for field_name in self.config.get('fields', {}).keys():
            if field_name in row_data:
                total_count += 1
                try:
                    element = self.config_manager.find_element_with_fallbacks(self.driver, field_name)
                    if element:
                        entered_value = element.get_attribute('value')
                        expected_value = str(row_data[field_name])
                        
                        if entered_value == expected_value:
                            correct_count += 1
                            print(f"  ✅ {field_name}: '{entered_value}'")
                        else:
                            print(f"  ❌ {field_name}: Expected '{expected_value}', Got '{entered_value}'")
                    else:
                        print(f"  ❌ {field_name}: Element not found")
                except Exception as e:
                    print(f"  ❌ {field_name}: Error - {e}")
        
        accuracy = (correct_count / total_count) * 100 if total_count > 0 else 0
        print(f"\n📊 Verification: {correct_count}/{total_count} correct ({accuracy:.1f}%)")
        
        return correct_count == total_count
    
    def get_user_confirmation(self, person_name, id_no):
        """Get simple terminal confirmation"""
        print(f"\n{'='*60}")
        print(f"🔍 MANUAL VERIFICATION REQUIRED")
        print(f"{'='*60}")
        print(f"👤 Person: {person_name}")
        print(f"🆔 ID NO.: {id_no}")
        print(f"{'='*60}")
        print("Please check the browser window:")
        print("1. ✅ Verify all 16 fields are filled correctly")
        print("2. ✅ Check for any formatting issues")
        print("3. ✅ Ensure no fields are empty")
        print("4. ✅ If everything looks good, manually SUBMIT the form")
        print(f"{'='*60}")
        
        while True:
            response = input("Did you submit the form? (yes/no/retry/quit): ").strip().lower()
            
            if response in ['yes', 'y']:
                print("✅ Form submitted - logging success")
                return 'submitted'
            elif response in ['no', 'n']:
                print("⏭️  Skipping this entry")
                return 'skip'
            elif response in ['retry', 'r']:
                print("🔄 Retrying this entry")
                return 'retry'
            elif response in ['quit', 'q']:
                print("🛑 Stopping batch processing")
                return 'quit'
            else:
                print("Please enter: yes, no, retry, or quit")
    
    def log_submission(self, row_data, status):
        """Log submission result"""
        log_entry = {
            'ID NO.': row_data.get('ID NO.', ''),
            'First Name': row_data.get('First Name', ''),
            'Last Name': row_data.get('Last Name', ''),
            'Status': status,
            'Timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Append to batch log
        df_log = pd.DataFrame([log_entry])
        log_file = 'batch_submission_log.csv'
        
        if os.path.exists(log_file):
            df_log.to_csv(log_file, mode='a', header=False, index=False)
        else:
            df_log.to_csv(log_file, mode='w', header=True, index=False)
    
    def run_batch_processing(self, start_row=None, end_row=None):
        """Run batch processing"""
        print("🚀 SafeFormBot - Batch Processing Mode")
        print("=" * 50)
        
        # Load configuration
        self.config = self.config_manager.load_config()
        if not self.config:
            print("❌ Failed to load configuration")
            return
        
        # Load data
        if not os.path.exists('input.csv'):
            print("❌ input.csv not found")
            return
        
        df = pd.read_csv('input.csv')
        print(f"📊 Loaded {len(df)} total entries")
        
        # Get protected entries
        protected_ids = self.get_submitted_entries()
        
        # Determine starting point
        if start_row is None:
            last_completed = self.load_progress()
            start_row = max(8, last_completed + 1)  # Start from 8th entry (Fermin) or where we left off
        
        print(f"🎯 Starting from entry {start_row}")
        
        # Determine end point
        if end_row is None:
            end_row = len(df)
        
        print(f"📋 Will process entries {start_row} to {end_row}")
        
        # Initialize browser
        print("\n🚀 Starting browser...")
        options = webdriver.ChromeOptions()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        self.driver = webdriver.Chrome(
            service=ChromeService(ChromeDriverManager().install()),
            options=options
        )
        
        try:
            stats = {'submitted': 0, 'skipped': 0, 'errors': 0}
            
            # Process each entry
            for row_idx in range(start_row - 1, min(end_row, len(df))):
                row = df.iloc[row_idx]
                entry_number = row_idx + 1
                
                # Skip if already submitted
                if str(row['ID NO.']) in protected_ids:
                    print(f"\n⏭️  Entry {entry_number}: {row['First Name']} {row['Last Name']} - Already submitted")
                    continue
                
                print(f"\n{'='*60}")
                print(f"🧑 PROCESSING ENTRY {entry_number}")
                print(f"👤 {row['First Name']} {row['Last Name']} (ID: {row['ID NO.']})")
                print(f"📊 Progress: {entry_number - start_row + 1}/{end_row - start_row + 1}")
                print(f"{'='*60}")
                
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        # Fill form
                        if self.fill_form_fields(dict(row)):
                            # Verify fields
                            if self.verify_fields(dict(row)):
                                # Get user confirmation
                                person_name = f"{row['First Name']} {row['Last Name']}"
                                decision = self.get_user_confirmation(person_name, row['ID NO.'])
                                
                                if decision == 'submitted':
                                    self.log_submission(dict(row), 'submitted')
                                    stats['submitted'] += 1
                                    self.submitted_count += 1
                                    protected_ids.add(str(row['ID NO.']))
                                    break
                                elif decision == 'skip':
                                    self.log_submission(dict(row), 'skipped')
                                    stats['skipped'] += 1
                                    break
                                elif decision == 'quit':
                                    print("🛑 User requested to quit")
                                    self.save_progress(entry_number - 1)
                                    return stats
                                # If retry, continue to next attempt
                            else:
                                print("❌ Field verification failed")
                                if attempt < max_retries - 1:
                                    print(f"🔄 Retrying... (Attempt {attempt + 2}/{max_retries})")
                                    time.sleep(2)
                        else:
                            print("❌ Form filling failed")
                            if attempt < max_retries - 1:
                                print(f"🔄 Retrying... (Attempt {attempt + 2}/{max_retries})")
                                time.sleep(2)
                                
                    except Exception as e:
                        print(f"❌ Error processing entry: {e}")
                        if attempt < max_retries - 1:
                            print(f"🔄 Retrying... (Attempt {attempt + 2}/{max_retries})")
                            time.sleep(2)
                        else:
                            self.log_submission(dict(row), f'error: {e}')
                            stats['errors'] += 1
                
                # Save progress after each entry
                self.save_progress(entry_number)
                
                # Show current stats
                print(f"\n📊 Current Stats:")
                print(f"  ✅ Submitted: {stats['submitted']}")
                print(f"  ⏭️  Skipped: {stats['skipped']}")
                print(f"  ❌ Errors: {stats['errors']}")
            
            print(f"\n🎉 BATCH PROCESSING COMPLETE!")
            print(f"✅ Successfully submitted: {stats['submitted']}")
            print(f"⏭️  Skipped: {stats['skipped']}")
            print(f"❌ Errors: {stats['errors']}")
            
            return stats
            
        finally:
            print("\n🔒 Closing browser...")
            self.driver.quit()


if __name__ == "__main__":
    bot = BatchSubmissionBot()
    
    # Start batch processing from entry 8 (Fermin Kimberly)
    print("🎯 Starting batch processing from entry 8 (Fermin Kimberly)")
    print("📋 You can process multiple entries in sequence")
    print("💬 Simple terminal confirmation - no popups!")
    
    # Process entries 8-12 as a test batch
    bot.run_batch_processing(start_row=8, end_row=12)
