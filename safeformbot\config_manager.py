"""
Enhanced Configuration Manager for SafeFormBot
Handles form configuration with validation, fallback selectors, and XPath testing
"""

import json
import re
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager


class ConfigManager:
    """Enhanced configuration manager with validation and fallback support"""
    
    def __init__(self, config_file: str = "form_config_enhanced.json"):
        self.config_file = config_file
        self.config = None
        self.validation_results = {}
        
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            return self.config
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file '{self.config_file}' not found")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file: {e}")
    
    def save_config(self, config: Dict[str, Any]) -> None:
        """Save configuration to JSON file"""
        config['last_modified'] = datetime.now().strftime("%Y-%m-%d")
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        self.config = config
    
    def validate_field_data(self, field_name: str, value: str) -> Tuple[bool, str]:
        """Validate field data against configuration rules"""
        if not self.config:
            self.load_config()
        
        field_config = self.config.get('fields', {}).get(field_name)
        if not field_config:
            return False, f"Field '{field_name}' not found in configuration"
        
        # Check if required field is empty
        if field_config.get('required', False) and not value.strip():
            return False, f"Required field '{field_name}' cannot be empty"
        
        # Skip validation for empty optional fields
        if not value.strip() and not field_config.get('required', False):
            return True, "Optional field is empty - OK"
        
        # Validate pattern
        pattern = field_config.get('validation_pattern')
        if pattern and not re.match(pattern, value):
            return False, f"Field '{field_name}' value '{value}' doesn't match pattern '{pattern}'"
        
        # Validate length
        max_length = field_config.get('max_length')
        if max_length and len(value) > max_length:
            return False, f"Field '{field_name}' value too long (max {max_length} chars)"
        
        # Validate numeric ranges
        if field_config.get('field_type') == 'number':
            try:
                num_value = float(value)
                min_val = field_config.get('min_value')
                max_val = field_config.get('max_value')
                
                if min_val is not None and num_value < min_val:
                    return False, f"Field '{field_name}' value {num_value} below minimum {min_val}"
                
                if max_val is not None and num_value > max_val:
                    return False, f"Field '{field_name}' value {num_value} above maximum {max_val}"
                    
            except ValueError:
                return False, f"Field '{field_name}' expected number but got '{value}'"
        
        return True, "Valid"
    
    def find_element_with_fallbacks(self, driver: webdriver.Chrome, field_name: str) -> Optional[Any]:
        """Find element using primary selector and fallbacks"""
        if not self.config:
            self.load_config()
        
        field_config = self.config.get('fields', {}).get(field_name)
        if not field_config:
            return None
        
        # Try primary selector first
        primary_selector = field_config.get('primary_selector')
        if primary_selector:
            try:
                element = driver.find_element(By.XPATH, primary_selector)
                return element
            except NoSuchElementException:
                pass
        
        # Try fallback selectors
        fallback_selectors = field_config.get('fallback_selectors', [])
        for selector in fallback_selectors:
            try:
                # Determine selector type
                if selector.startswith('//') or selector.startswith('/'):
                    element = driver.find_element(By.XPATH, selector)
                else:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                return element
            except NoSuchElementException:
                continue
        
        return None
    
    def validate_selectors(self, form_url: str = None, headless: bool = True) -> Dict[str, Any]:
        """Validate all selectors by testing them on the actual form"""
        if not self.config:
            self.load_config()
        
        url = form_url or self.config.get('form_url')
        if not url:
            return {"error": "No form URL provided"}
        
        # Initialize WebDriver
        options = webdriver.ChromeOptions()
        if headless:
            options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(
            service=ChromeService(ChromeDriverManager().install()),
            options=options
        )
        
        results = {
            "form_url": url,
            "validation_timestamp": datetime.now().isoformat(),
            "fields": {},
            "submit_button": {},
            "summary": {"total": 0, "valid": 0, "invalid": 0}
        }
        
        try:
            driver.get(url)
            time.sleep(self.config.get('form_settings', {}).get('wait_after_load', 3))
            
            # Validate field selectors
            for field_name, field_config in self.config.get('fields', {}).items():
                results["fields"][field_name] = self._validate_field_selector(driver, field_name, field_config)
                results["summary"]["total"] += 1
                if results["fields"][field_name]["status"] == "valid":
                    results["summary"]["valid"] += 1
                else:
                    results["summary"]["invalid"] += 1
            
            # Validate submit button
            submit_config = self.config.get('submit_button', {})
            results["submit_button"] = self._validate_submit_selector(driver, submit_config)
            
        except Exception as e:
            results["error"] = str(e)
        finally:
            driver.quit()
        
        self.validation_results = results
        return results
    
    def _validate_field_selector(self, driver: webdriver.Chrome, field_name: str, field_config: Dict) -> Dict:
        """Validate a single field selector"""
        result = {
            "field_name": field_name,
            "status": "invalid",
            "working_selector": None,
            "selector_type": None,
            "element_info": {},
            "errors": []
        }
        
        # Try primary selector
        primary_selector = field_config.get('primary_selector')
        if primary_selector:
            try:
                element = driver.find_element(By.XPATH, primary_selector)
                result.update({
                    "status": "valid",
                    "working_selector": primary_selector,
                    "selector_type": "primary_xpath",
                    "element_info": self._get_element_info(element)
                })
                return result
            except NoSuchElementException:
                result["errors"].append(f"Primary XPath failed: {primary_selector}")
        
        # Try fallback selectors
        fallback_selectors = field_config.get('fallback_selectors', [])
        for i, selector in enumerate(fallback_selectors):
            try:
                if selector.startswith('//') or selector.startswith('/'):
                    element = driver.find_element(By.XPATH, selector)
                    selector_type = f"fallback_xpath_{i+1}"
                else:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                    selector_type = f"fallback_css_{i+1}"
                
                result.update({
                    "status": "valid",
                    "working_selector": selector,
                    "selector_type": selector_type,
                    "element_info": self._get_element_info(element)
                })
                return result
            except NoSuchElementException:
                result["errors"].append(f"Fallback selector {i+1} failed: {selector}")
        
        return result
    
    def _validate_submit_selector(self, driver: webdriver.Chrome, submit_config: Dict) -> Dict:
        """Validate submit button selector"""
        result = {
            "status": "invalid",
            "working_selector": None,
            "selector_type": None,
            "element_info": {},
            "errors": []
        }
        
        # Try primary selector
        primary_selector = submit_config.get('primary_selector')
        if primary_selector:
            try:
                element = driver.find_element(By.XPATH, primary_selector)
                result.update({
                    "status": "valid",
                    "working_selector": primary_selector,
                    "selector_type": "primary_xpath",
                    "element_info": self._get_element_info(element)
                })
                return result
            except NoSuchElementException:
                result["errors"].append(f"Primary submit selector failed: {primary_selector}")
        
        # Try fallback selectors
        fallback_selectors = submit_config.get('fallback_selectors', [])
        for i, selector in enumerate(fallback_selectors):
            try:
                if selector.startswith('//') or selector.startswith('/'):
                    element = driver.find_element(By.XPATH, selector)
                    selector_type = f"fallback_xpath_{i+1}"
                else:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                    selector_type = f"fallback_css_{i+1}"
                
                result.update({
                    "status": "valid",
                    "working_selector": selector,
                    "selector_type": selector_type,
                    "element_info": self._get_element_info(element)
                })
                return result
            except NoSuchElementException:
                result["errors"].append(f"Fallback submit selector {i+1} failed: {selector}")
        
        return result
    
    def _get_element_info(self, element) -> Dict:
        """Get information about a web element"""
        try:
            return {
                "tag_name": element.tag_name,
                "id": element.get_attribute('id') or '',
                "class": element.get_attribute('class') or '',
                "name": element.get_attribute('name') or '',
                "type": element.get_attribute('type') or '',
                "aria_label": element.get_attribute('aria-label') or '',
                "placeholder": element.get_attribute('placeholder') or '',
                "visible": element.is_displayed(),
                "enabled": element.is_enabled()
            }
        except Exception:
            return {"error": "Could not retrieve element info"}
    
    def generate_validation_report(self) -> str:
        """Generate a human-readable validation report"""
        if not self.validation_results:
            return "No validation results available. Run validate_selectors() first."
        
        report = []
        report.append("=== SAFEFORMBOT SELECTOR VALIDATION REPORT ===")
        report.append(f"Form URL: {self.validation_results.get('form_url', 'N/A')}")
        report.append(f"Validation Time: {self.validation_results.get('validation_timestamp', 'N/A')}")
        report.append("")
        
        summary = self.validation_results.get('summary', {})
        report.append(f"SUMMARY: {summary.get('valid', 0)}/{summary.get('total', 0)} fields valid")
        report.append("")
        
        # Field validation results
        report.append("FIELD VALIDATION RESULTS:")
        report.append("-" * 50)
        
        for field_name, result in self.validation_results.get('fields', {}).items():
            status = "✅ VALID" if result['status'] == 'valid' else "❌ INVALID"
            report.append(f"{field_name}: {status}")
            
            if result['status'] == 'valid':
                report.append(f"  Working Selector: {result['working_selector']}")
                report.append(f"  Selector Type: {result['selector_type']}")
            else:
                report.append("  Errors:")
                for error in result.get('errors', []):
                    report.append(f"    - {error}")
            report.append("")
        
        # Submit button validation
        submit_result = self.validation_results.get('submit_button', {})
        status = "✅ VALID" if submit_result.get('status') == 'valid' else "❌ INVALID"
        report.append(f"Submit Button: {status}")
        if submit_result.get('status') == 'valid':
            report.append(f"  Working Selector: {submit_result.get('working_selector')}")
        else:
            report.append("  Errors:")
            for error in submit_result.get('errors', []):
                report.append(f"    - {error}")
        
        return "\n".join(report)


if __name__ == "__main__":
    # Example usage
    config_manager = ConfigManager()
    
    # Load and validate configuration
    try:
        config = config_manager.load_config()
        print("Configuration loaded successfully!")
        
        # Validate selectors (this will open a browser)
        print("Validating selectors...")
        results = config_manager.validate_selectors(headless=True)
        
        # Generate report
        report = config_manager.generate_validation_report()
        print(report)
        
        # Save validation results
        with open('validation_report.txt', 'w') as f:
            f.write(report)
        print("\nValidation report saved to 'validation_report.txt'")
        
    except Exception as e:
        print(f"Error: {e}")
