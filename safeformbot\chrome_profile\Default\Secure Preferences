{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\135.0.7049.42\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\135.0.7049.42\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"browser": {"show_home_button": "568C3809CF0A536CA201F56EE7BF220C8EB6C1FDD543D58C9AD136B3290BCBC9"}, "default_search_provider_data": {"template_url_data": "00265EF779CF20B7764C457B34CDD9420F3D87CDB7989FC97649328B3EF50A06"}, "enterprise_signin": {"policy_recovery_token": "7E04089BF22C707C97BB608AF5E2CBE6478FC685A5FBF3CE0FDCE1DC12D9B57F"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "02A4A369199C9784607DC350CBDC408A8CFFE019308FEFC10A968491214AEED0", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "90718524E7E1004A0C4473479B4070D48FB08B468F34A2B7B4022E66E05C1C77"}, "ui": {"developer_mode": "FD363C254CD5A0D2632CEEFAC3B788082E768088DFFC03038BFBEF7111155957"}}, "google": {"services": {"account_id": "3BAB41D69BE22455F74262D9B7A2BBD3923C0806C200958CAC55F833C878BE94", "last_signed_in_username": "29111811B787F81B27046054B47786C2C95C87DD55C53F2BAAE370B11E5EEC17", "last_username": "BB33A00733516872568C02170E99EA8653740703B247E7EC24328F8D59AF5742"}}, "homepage": "05CF66A9673D9E4CC140E410771930CC9D7E9958EE8E9A4FCFCF26CF89198252", "homepage_is_newtabpage": "5EF4EF0B4F22D9414D3BAB3DC2B2B3536593F33741DB21A17DAFCDF2BAC3F71C", "media": {"cdm": {"origin_data": "12DDA10CD90439B663B120E79C2C8885B9C02324968D5C95FFA691ED4930CD40"}, "storage_id_salt": "62AAF8D3A65187481055EC11B30CD17958BAFD76A839020808A13597D1535354"}, "module_blocklist_cache_md5_digest": "2C791E9CFFA07DA2247CACF9A65985D83D4C46070F4997ABDC25A408A6F1F0F4", "pinned_tabs": "F4F5FAA7CE40ED2F562BC00CF454809F8F8894831652A537861B7F614CA1D72E", "prefs": {"preference_reset_time": "34DFA3444A21B3B7DCAB6C9D0A24DB490E9367D45418E8A460A1A48D45D600FB"}, "safebrowsing": {"incidents_sent": "A3A90F91C1ED0BB2DA6FA76460AD05813EB82A91B753411F0E0CAB46298E63BD"}, "search_provider_overrides": "E586B5505D418F738AC87F8F3088A4E15089614975A0DBEEF91A6A2995BBF3CA", "session": {"restore_on_startup": "D1A29354329D01901BDC89864662D43F8116029E60A375C311DD41B6F66A9B3A", "startup_urls": "AC187E9C00B936C1E479EE4E45AA99A8DFCFAE5C0F8344DF56CEB73983B0BAE4"}}, "super_mac": "A14000C45273542DD2D80DDFFC87782E4E7882D04AA1247B89B4BF87D98109DF"}}