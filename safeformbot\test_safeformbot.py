"""
Test Suite for SafeFormBot v2.0
Comprehensive testing of all components
"""

import unittest
import os
import json
import pandas as pd
from datetime import datetime
import tempfile
import shutil

# Import SafeFormBot components
from config_manager import ConfigManager
from submission_manager import SubmissionManager
from analytics_dashboard import PerformanceAnalyzer


class TestConfigManager(unittest.TestCase):
    """Test the enhanced configuration manager"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_config = {
            "version": "2.0",
            "form_name": "Test Form",
            "form_url": "https://example.com/form",
            "fields": {
                "Test Field": {
                    "primary_selector": "//input[@name='test']",
                    "fallback_selectors": ["input[name='test']"],
                    "field_type": "text",
                    "required": True,
                    "validation_pattern": "^[A-Za-z]+$",
                    "max_length": 50
                }
            },
            "submit_button": {
                "primary_selector": "//button[@type='submit']",
                "fallback_selectors": ["button[type='submit']"]
            }
        }
        
        # Create temporary config file
        self.temp_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.test_config, self.temp_config_file)
        self.temp_config_file.close()
        
        self.config_manager = ConfigManager(self.temp_config_file.name)
    
    def tearDown(self):
        """Clean up test environment"""
        os.unlink(self.temp_config_file.name)
    
    def test_load_config(self):
        """Test configuration loading"""
        config = self.config_manager.load_config()
        self.assertIsNotNone(config)
        self.assertEqual(config['version'], '2.0')
        self.assertEqual(config['form_name'], 'Test Form')
    
    def test_validate_field_data(self):
        """Test field data validation"""
        # Valid data
        is_valid, message = self.config_manager.validate_field_data('Test Field', 'ValidText')
        self.assertTrue(is_valid)
        
        # Invalid data (contains numbers)
        is_valid, message = self.config_manager.validate_field_data('Test Field', 'Invalid123')
        self.assertFalse(is_valid)
        
        # Empty required field
        is_valid, message = self.config_manager.validate_field_data('Test Field', '')
        self.assertFalse(is_valid)
    
    def test_save_config(self):
        """Test configuration saving"""
        modified_config = self.test_config.copy()
        modified_config['form_name'] = 'Modified Test Form'
        
        self.config_manager.save_config(modified_config)
        
        # Reload and verify
        reloaded_config = self.config_manager.load_config()
        self.assertEqual(reloaded_config['form_name'], 'Modified Test Form')


class TestSubmissionManager(unittest.TestCase):
    """Test the submission management system"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.original_dir = os.getcwd()
        os.chdir(self.temp_dir)
        
        self.submission_manager = SubmissionManager()
        
        # Sample row data
        self.sample_row = {
            'ID NO.': '123456',
            'First Name': 'John',
            'Last Name': 'Doe',
            'Age': '25'
        }
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_dir)
        shutil.rmtree(self.temp_dir)
    
    def test_add_pending_submission(self):
        """Test adding pending submissions"""
        submission_id = self.submission_manager.add_pending_submission(self.sample_row)
        self.assertIsNotNone(submission_id)
        self.assertTrue(submission_id.startswith('123456_'))
        
        # Check if file was created
        self.assertTrue(os.path.exists('pending_submissions.csv'))
        
        # Check content
        df = pd.read_csv('pending_submissions.csv')
        self.assertEqual(len(df), 1)
        self.assertEqual(df.iloc[0]['First Name'], 'John')
    
    def test_confirm_submission(self):
        """Test submission confirmation"""
        submission_id = self.submission_manager.add_pending_submission(self.sample_row)
        
        # Confirm the submission
        result = self.submission_manager.confirm_submission(submission_id, "Test confirmation")
        self.assertTrue(result)
        
        # Check if moved to confirmed file
        self.assertTrue(os.path.exists('confirmed_submissions.csv'))
        
        # Check pending file is empty
        df_pending = self.submission_manager.get_pending_submissions()
        self.assertEqual(len(df_pending), 0)
    
    def test_batch_confirm_submissions(self):
        """Test batch confirmation"""
        # Add multiple submissions
        submission_ids = []
        for i in range(3):
            row = self.sample_row.copy()
            row['ID NO.'] = f'12345{i}'
            submission_id = self.submission_manager.add_pending_submission(row)
            submission_ids.append(submission_id)
        
        # Batch confirm
        results = self.submission_manager.batch_confirm_submissions(submission_ids)
        
        self.assertEqual(len(results['success']), 3)
        self.assertEqual(len(results['failed']), 0)
    
    def test_get_submission_stats(self):
        """Test submission statistics"""
        # Add some submissions
        submission_id1 = self.submission_manager.add_pending_submission(self.sample_row)
        
        row2 = self.sample_row.copy()
        row2['ID NO.'] = '789012'
        submission_id2 = self.submission_manager.add_pending_submission(row2)
        
        # Confirm one
        self.submission_manager.confirm_submission(submission_id1)
        
        # Get stats
        stats = self.submission_manager.get_submission_stats()
        
        self.assertEqual(stats['pending'], 1)
        self.assertEqual(stats['confirmed'], 1)
        self.assertEqual(stats['total'], 2)


class TestPerformanceAnalyzer(unittest.TestCase):
    """Test the performance analytics system"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.original_dir = os.getcwd()
        os.chdir(self.temp_dir)
        
        self.analyzer = PerformanceAnalyzer()
        
        # Create sample data files
        self.create_sample_data()
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_dir)
        shutil.rmtree(self.temp_dir)
    
    def create_sample_data(self):
        """Create sample data files for testing"""
        # Create sample submission log
        submission_data = {
            'ID NO.': ['123456', '789012', '345678'],
            'First Name': ['John', 'Jane', 'Bob'],
            'Last Name': ['Doe', 'Smith', 'Johnson'],
            'Timestamp': [
                '2025-01-18_10-00-00',
                '2025-01-18_10-05-00',
                '2025-01-18_10-10-00'
            ]
        }
        pd.DataFrame(submission_data).to_csv('submission_log.csv', index=False)
        
        # Create sample errors
        error_data = {
            'Name': ['Alice Brown'],
            'Error': ['xpath not found - major error'],
            'Timestamp': ['2025-01-18_10-15-00']
        }
        pd.DataFrame(error_data).to_csv('errors.csv', index=False)
        
        # Create confirmed submissions
        confirmed_data = {
            'submission_id': ['123456_2025-01-18_10-00-00', '789012_2025-01-18_10-05-00'],
            'First Name': ['John', 'Jane'],
            'Last Name': ['Doe', 'Smith'],
            'status': ['CONFIRMED', 'CONFIRMED']
        }
        pd.DataFrame(confirmed_data).to_csv('confirmed_submissions.csv', index=False)
    
    def test_calculate_accuracy_metrics(self):
        """Test accuracy metrics calculation"""
        metrics = self.analyzer.calculate_accuracy_metrics()
        
        self.assertIsInstance(metrics, dict)
        self.assertIn('total_submissions', metrics)
        self.assertIn('accuracy_percentage', metrics)
        self.assertIn('minor_errors', metrics)
        self.assertIn('major_errors', metrics)
        
        # Should have 2 successful + 1 failed = 3 total
        self.assertEqual(metrics['total_submissions'], 3)
        self.assertEqual(metrics['successful_submissions'], 2)
        self.assertEqual(metrics['failed_submissions'], 1)
        self.assertEqual(metrics['major_errors'], 1)  # xpath not found is major
    
    def test_analyze_error_patterns(self):
        """Test error pattern analysis"""
        error_analysis = self.analyzer.analyze_error_patterns()
        
        self.assertIsInstance(error_analysis, dict)
        self.assertIn('most_common_errors', error_analysis)
        
        # Should find our test error
        if error_analysis['most_common_errors']:
            self.assertEqual(error_analysis['most_common_errors'][0]['count'], 1)
    
    def test_generate_quality_report(self):
        """Test quality report generation"""
        report = self.analyzer.generate_quality_report()
        
        self.assertIsInstance(report, str)
        self.assertIn('SAFEFORMBOT QUALITY CONTROL REPORT', report)
        self.assertIn('ACCURACY METRICS', report)
        self.assertIn('PLAN STATUS EVALUATION', report)
    
    def test_plan_status_evaluation(self):
        """Test Gold/VIP plan status evaluation"""
        metrics = self.analyzer.calculate_accuracy_metrics()
        
        # With our test data, should pass VIP but might fail Gold depending on accuracy
        self.assertIn(metrics['vip_plan_status'], ['PASSED', 'FAILED'])
        self.assertIn(metrics['gold_plan_status'], ['PASSED', 'FAILED'])


class TestIntegration(unittest.TestCase):
    """Integration tests for SafeFormBot components"""
    
    def setUp(self):
        """Set up integration test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.original_dir = os.getcwd()
        os.chdir(self.temp_dir)
        
        # Create sample input.csv
        sample_data = {
            'ID NO.': ['123456', '789012'],
            'First Name': ['John', 'Jane'],
            'Last Name': ['Doe', 'Smith'],
            'Age': ['25', '30']
        }
        pd.DataFrame(sample_data).to_csv('input.csv', index=False)
    
    def tearDown(self):
        """Clean up integration test environment"""
        os.chdir(self.original_dir)
        shutil.rmtree(self.temp_dir)
    
    def test_end_to_end_workflow(self):
        """Test complete workflow from configuration to analytics"""
        # 1. Create configuration
        config = {
            "version": "2.0",
            "form_url": "https://example.com/form",
            "fields": {
                "First Name": {
                    "primary_selector": "//input[@name='fname']",
                    "field_type": "text",
                    "required": True
                }
            },
            "submit_button": {
                "primary_selector": "//button[@type='submit']"
            }
        }
        
        with open('form_config_enhanced.json', 'w') as f:
            json.dump(config, f)
        
        # 2. Test configuration loading
        config_manager = ConfigManager('form_config_enhanced.json')
        loaded_config = config_manager.load_config()
        self.assertIsNotNone(loaded_config)
        
        # 3. Test submission management
        submission_manager = SubmissionManager()
        
        # Simulate adding submissions
        df = pd.read_csv('input.csv')
        for _, row in df.iterrows():
            submission_manager.add_pending_submission(dict(row))
        
        stats = submission_manager.get_submission_stats()
        self.assertEqual(stats['pending'], 2)
        
        # 4. Test analytics (with minimal data)
        analyzer = PerformanceAnalyzer()
        metrics = analyzer.calculate_accuracy_metrics()
        self.assertIsInstance(metrics, dict)


def run_tests():
    """Run all tests"""
    print("🧪 Running SafeFormBot Test Suite...")
    print("=" * 50)

    # Create test suite
    loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()

    # Add test cases
    test_suite.addTests(loader.loadTestsFromTestCase(TestConfigManager))
    test_suite.addTests(loader.loadTestsFromTestCase(TestSubmissionManager))
    test_suite.addTests(loader.loadTestsFromTestCase(TestPerformanceAnalyzer))
    test_suite.addTests(loader.loadTestsFromTestCase(TestIntegration))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    if result.wasSuccessful():
        print("\n✅ All tests passed!")
        return True
    else:
        print("\n❌ Some tests failed!")
        return False


if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)
