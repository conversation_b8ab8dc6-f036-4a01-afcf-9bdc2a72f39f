"""
Manually log Numbers <PERSON><PERSON> as submitted
"""

import pandas as pd
import os
from datetime import datetime

def log_submission():
    """Log Numbers <PERSON><PERSON> as submitted"""
    
    # Submission data
    log_entry = {
        'ID NO.': '254619',
        'First Name': 'Numbers',
        'Last Name': '<PERSON>',
        'Status': 'submitted',
        'Timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'Notes': 'Manually filled Age field (41.95), then submitted'
    }
    
    # Log to safe_submission_log.csv
    df_log = pd.DataFrame([log_entry])
    log_file = 'safe_submission_log.csv'
    
    if os.path.exists(log_file):
        df_log.to_csv(log_file, mode='a', header=False, index=False)
    else:
        df_log.to_csv(log_file, mode='w', header=True, index=False)
    
    print("✅ Numbers E. Dominique (ID: 254619) logged as submitted")
    print("📝 Note: Age field was manually corrected to 41.95")

if __name__ == "__main__":
    log_submission()
