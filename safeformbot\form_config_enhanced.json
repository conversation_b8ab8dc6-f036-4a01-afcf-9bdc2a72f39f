{"version": "2.0", "form_name": "Sample Data Entry Form", "form_url": "https://docs.google.com/forms/d/e/1FAIpQLSfrorZVKQbYGWTBjpP7bgECZHRuZsGu9f0IP6P6QxHjHE8-sw/viewform", "created_date": "2025-01-18", "last_modified": "2025-01-18", "validation_settings": {"validate_before_run": true, "max_validation_attempts": 3, "validation_timeout": 10}, "fields": {"ID NO.": {"primary_selector": "//*[contains(text(), \"ID NO.\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='ID NO']", "input[placeholder*='ID NO']", "#id_no, #idno, [name*='id_no']"], "field_type": "text", "required": true, "validation_pattern": "^[0-9]+$", "max_length": 10}, "First Name": {"primary_selector": "//*[contains(text(), \"First Name\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='First Name']", "input[placeholder*='First Name']", "#first_name, [name*='first_name'], [name*='fname']"], "field_type": "text", "required": true, "validation_pattern": "^[A-Za-z\\s]+$", "max_length": 50}, "Middle Initial": {"primary_selector": "//*[contains(text(), \"Middle Initial\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='Middle Initial']", "input[placeholder*='Middle Initial']", "#middle_initial, [name*='middle_initial'], [name*='mi']"], "field_type": "text", "required": false, "validation_pattern": "^[A-Za-z]?$", "max_length": 1}, "Last Name": {"primary_selector": "//*[contains(text(), \"Last Name\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='Last Name']", "input[placeholder*='Last Name']", "#last_name, [name*='last_name'], [name*='lname']"], "field_type": "text", "required": true, "validation_pattern": "^[A-Za-z\\s]+$", "max_length": 50}, "Father's Name": {"primary_selector": "//*[contains(text(), \"Father's Name\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='Father']", "input[placeholder*='Father']", "#father_name, [name*='father_name']"], "field_type": "text", "required": true, "validation_pattern": "^[A-Za-z\\s]+$", "max_length": 50}, "Mother's Name": {"primary_selector": "//*[contains(text(), \"Mother's Name\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='Mother']", "input[placeholder*='Mother']", "#mother_name, [name*='mother_name']"], "field_type": "text", "required": true, "validation_pattern": "^[A-Za-z\\s]+$", "max_length": 50}, "Mother's Maiden Name": {"primary_selector": "//*[contains(text(), \"<PERSON>'s Maiden Name\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='Maiden']", "input[placeholder*='Maiden']", "#maiden_name, [name*='maiden_name']"], "field_type": "text", "required": true, "validation_pattern": "^[A-Za-z\\s]+$", "max_length": 50}, "Age": {"primary_selector": "//*[contains(text(), \"Age\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='Age']", "input[placeholder*='Age']", "#age, [name*='age']"], "field_type": "number", "required": true, "validation_pattern": "^[0-9]+(\\.[0-9]+)?$", "min_value": 18, "max_value": 100}, "Weight in Kgs.": {"primary_selector": "//*[contains(text(), \"Weight in Kgs.\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='Weight']", "input[placeholder*='Weight']", "#weight, [name*='weight']"], "field_type": "number", "required": true, "validation_pattern": "^[0-9]+(\\.[0-9]+)?$", "min_value": 30, "max_value": 200}, "Age in Company (Years)": {"primary_selector": "//*[contains(text(), \"Age in Company (Years)\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='Company']", "input[placeholder*='Company']", "#company_age, [name*='company_age']"], "field_type": "number", "required": true, "validation_pattern": "^[0-9]+(\\.[0-9]+)?$", "min_value": 0, "max_value": 50}, " % Hike Salary": {"primary_selector": "//*[contains(text(), \" % Hike Salary\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='Hike']", "input[placeholder*='Hike']", "#salary_hike, [name*='salary_hike']"], "field_type": "text", "required": true, "validation_pattern": "^[0-9]+%?$", "max_length": 10}, "Register joining code": {"primary_selector": "//*[contains(text(), \"Register joining code\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='Register']", "input[placeholder*='Register']", "#register_code, [name*='register_code']"], "field_type": "text", "required": true, "validation_pattern": "^[0-9]{3}-[0-9]{2}-[0-9]{4}$", "max_length": 15}, "Place Name": {"primary_selector": "//*[contains(text(), \"Place Name\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='Place']", "input[placeholder*='Place']", "#place_name, [name*='place_name']"], "field_type": "text", "required": true, "validation_pattern": "^[A-Za-z\\s]+$", "max_length": 100}, "County": {"primary_selector": "//*[contains(text(), \"County\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='County']", "input[placeholder*='County']", "#county, [name*='county']"], "field_type": "text", "required": true, "validation_pattern": "^[A-Za-z\\s]+$", "max_length": 50}, "City": {"primary_selector": "//*[contains(text(), \"City\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='City']", "input[placeholder*='City']", "#city, [name*='city']"], "field_type": "text", "required": true, "validation_pattern": "^[A-Za-z\\s]+$", "max_length": 50}, "Region": {"primary_selector": "//*[contains(text(), \"Region\")]/ancestor::div[contains(@class, 'geS5n')]//input", "fallback_selectors": ["input[aria-label*='Region']", "input[placeholder*='Region']", "#region, [name*='region']"], "field_type": "text", "required": true, "validation_pattern": "^[A-Za-z\\s]+$", "max_length": 50}}, "submit_button": {"primary_selector": "//span[text()='Submit']/../..", "fallback_selectors": ["button[type='submit']", "input[type='submit']", "[role='button']:contains('Submit')", ".freebirdFormviewerViewNavigationSubmitButton"]}, "form_settings": {"wait_after_load": 3, "wait_between_fields": 0.5, "wait_before_submit": 2, "max_retries": 3, "screenshot_on_error": true, "validate_each_field": true}, "confirmation_page_text": "Your response has been recorded."}