"""
SMART AUTO-DETECTION BOT
Automatically detects when form is submitted successfully and moves to next
No need to type "submitted" every time!
"""

import pandas as pd
import time
import random
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pyperclip
from selenium.webdriver.common.keys import Keys
from config_manager import ConfigManager


class SmartAutoBot:
    """Smart bot that auto-detects successful form submissions"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.config = None
        self.driver = None
        self.batch_log_file = "smart_auto_log.csv"
        
    def fill_single_form(self, row_data, entry_number):
        """Fill form for one person"""
        try:
            print(f"\n📝 Filling form for: {row_data['First Name']} {row_data['Last Name']} (ID: {row_data['ID NO.']})")
            
            # Navigate to form
            form_url = self.config.get('form_url')
            self.driver.get(form_url)
            time.sleep(3)
            
            # Fill all fields quickly
            filled_count = 0
            for field_name in self.config.get('fields', {}).keys():
                if field_name in row_data:
                    value = str(row_data[field_name])
                    try:
                        element = self.config_manager.find_element_with_fallbacks(self.driver, field_name)
                        if element:
                            element.clear()
                            pyperclip.copy(value)
                            element.send_keys(Keys.CONTROL, 'v')
                            time.sleep(0.3)
                            filled_count += 1
                            print(f"  ✅ {field_name}: {value}")
                    except:
                        print(f"  ❌ {field_name}: Failed")
            
            print(f"\n📊 Filled {filled_count}/16 fields")
            return filled_count >= 15
            
        except Exception as e:
            print(f"❌ Error filling form: {e}")
            return False
    
    def wait_for_submission_auto(self, person_name, id_no, entry_number, max_wait_minutes=5):
        """Auto-detect when form is submitted successfully"""
        print(f"\n{'='*70}")
        print(f"🤖 AUTO-DETECTION MODE")
        print(f"{'='*70}")
        print(f"👤 Person: {person_name}")
        print(f"🆔 ID NO.: {id_no}")
        print(f"📊 Entry: {entry_number}")
        print(f"{'='*70}")
        print("🔍 INSTRUCTIONS:")
        print("1. ✅ Check the browser - verify all 16 fields are correct")
        print("2. ✅ Manually SUBMIT the form")
        print("3. 🤖 Bot will AUTO-DETECT successful submission")
        print("4. 🚨 Only interrupt if there's an error!")
        print(f"{'='*70}")
        print(f"🤖 Waiting for submission... (max {max_wait_minutes} minutes)")
        print("💡 Type 'error' if something goes wrong, otherwise just submit the form!")
        
        # Start monitoring for success indicators
        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60
        
        # Check for success indicators every few seconds
        while time.time() - start_time < max_wait_seconds:
            try:
                # Method 1: Check for "Your response has been recorded" message
                success_messages = [
                    "Your response has been recorded",
                    "Thank you",
                    "Response recorded",
                    "Successfully submitted",
                    "Form submitted"
                ]
                
                page_text = self.driver.page_source.lower()
                for message in success_messages:
                    if message.lower() in page_text:
                        print(f"✅ SUCCESS DETECTED: Found '{message}' on page")
                        return 'submitted'
                
                # Method 2: Check URL change (Google Forms redirects after submission)
                current_url = self.driver.current_url
                if "formResponse" in current_url or "thanks" in current_url.lower():
                    print(f"✅ SUCCESS DETECTED: URL changed to success page")
                    return 'submitted'
                
                # Method 3: Check if submit button is gone/disabled
                try:
                    submit_buttons = self.driver.find_elements(By.XPATH, "//span[text()='Submit']/..")
                    if not submit_buttons:
                        print(f"✅ SUCCESS DETECTED: Submit button disappeared")
                        return 'submitted'
                except:
                    pass
                
                # Method 4: Check for confirmation elements
                try:
                    confirmation_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'recorded') or contains(text(), 'submitted') or contains(text(), 'Thank you')]")
                    if confirmation_elements:
                        print(f"✅ SUCCESS DETECTED: Found confirmation element")
                        return 'submitted'
                except:
                    pass
                
                # Check for user input (error reporting)
                # This is a simplified check - in a real implementation, you'd use threading
                time.sleep(2)  # Check every 2 seconds
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(2)
        
        # Timeout reached
        print(f"⏰ TIMEOUT: No submission detected after {max_wait_minutes} minutes")
        print("🤔 Please check if the form was submitted successfully")
        
        # Ask user for confirmation
        while True:
            response = input("Was the form submitted successfully? (yes/no/retry): ").strip().lower()
            if response in ['yes', 'y']:
                return 'submitted'
            elif response in ['no', 'n']:
                return 'failed'
            elif response in ['retry', 'r']:
                return 'retry'
            else:
                print("Please enter yes, no, or retry")
    
    def log_result(self, row_data, entry_number, status):
        """Log the result"""
        log_entry = {
            'Entry_Number': entry_number,
            'ID_NO': row_data.get('ID NO.', ''),
            'First_Name': row_data.get('First Name', ''),
            'Last_Name': row_data.get('Last Name', ''),
            'Status': status,
            'Timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        df = pd.DataFrame([log_entry])
        
        if os.path.exists(self.batch_log_file):
            df.to_csv(self.batch_log_file, mode='a', header=False, index=False)
        else:
            df.to_csv(self.batch_log_file, mode='w', header=True, index=False)
    
    def run_smart_auto_batch(self, start_entry=9, batch_size=50):
        """Run smart auto-detection batch processing"""
        print("🤖 SMART AUTO-DETECTION BOT")
        print("=" * 50)
        print("✅ Fills form → Auto-detects submission → Moves to next")
        print("✅ NO need to type 'submitted' every time!")
        print("✅ Only interrupt if there's an error")
        print("=" * 50)
        
        # Load configuration
        self.config = self.config_manager.load_config()
        if not self.config:
            print("❌ Failed to load configuration")
            return
        
        # Load data
        df = pd.read_csv('input.csv')
        
        print(f"📊 Starting from entry {start_entry}")
        print(f"📋 Will process {batch_size} entries in this session")
        print(f"🎯 Target: Process forms quickly with auto-detection")
        
        # Confirm start
        response = input(f"\nStart SMART AUTO processing from entry {start_entry}? (yes/no): ").strip().lower()
        if response not in ['yes', 'y']:
            print("❌ Cancelled")
            return
        
        # Initialize browser
        print("\n🚀 Starting browser...")
        options = webdriver.ChromeOptions()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        self.driver = webdriver.Chrome(
            service=ChromeService(ChromeDriverManager().install()),
            options=options
        )
        
        stats = {'submitted': 0, 'failed': 0, 'errors': 0}
        
        try:
            for i in range(batch_size):
                entry_number = start_entry + i
                
                if entry_number > len(df):
                    print(f"✅ Reached end of data at entry {entry_number}")
                    break
                
                row = df.iloc[entry_number - 1]
                person_name = f"{row['First Name']} {row['Last Name']}"
                
                print(f"\n{'='*70}")
                print(f"🧑 PROCESSING ENTRY {entry_number}")
                print(f"👤 {person_name} (ID: {row['ID NO.']})")
                print(f"📊 Progress: {i+1}/{batch_size}")
                print(f"⏱️ Estimated remaining: {(batch_size-i-1)*3} minutes")
                print(f"{'='*70}")
                
                try:
                    # Fill form
                    if self.fill_single_form(dict(row), entry_number):
                        # Auto-detect submission
                        result = self.wait_for_submission_auto(person_name, row['ID NO.'], entry_number)
                        
                        if result == 'submitted':
                            self.log_result(dict(row), entry_number, 'submitted')
                            stats['submitted'] += 1
                            print(f"✅ Entry {entry_number} AUTO-DETECTED as submitted!")
                        elif result == 'failed':
                            self.log_result(dict(row), entry_number, 'failed')
                            stats['failed'] += 1
                            print(f"❌ Entry {entry_number} failed")
                        elif result == 'retry':
                            print(f"🔄 Retrying entry {entry_number}...")
                            i -= 1  # Retry this entry
                            continue
                    else:
                        print(f"❌ Form filling failed for entry {entry_number}")
                        self.log_result(dict(row), entry_number, 'fill_failed')
                        stats['errors'] += 1
                        
                except Exception as e:
                    print(f"❌ Error processing entry {entry_number}: {e}")
                    self.log_result(dict(row), entry_number, f'error: {e}')
                    stats['errors'] += 1
                
                # Show progress
                print(f"\n📊 CURRENT PROGRESS:")
                print(f"  ✅ Submitted: {stats['submitted']}")
                print(f"  ❌ Failed: {stats['failed']}")
                print(f"  🔧 Errors: {stats['errors']}")
                print(f"  📈 Success Rate: {(stats['submitted']/(i+1)*100):.1f}%")
                
                # Brief pause before next entry
                if i < batch_size - 1:
                    print(f"\n⏳ Moving to next entry in 3 seconds...")
                    time.sleep(3)
            
            print(f"\n🎉 SMART AUTO BATCH COMPLETE!")
            print(f"✅ Successfully submitted: {stats['submitted']}")
            print(f"❌ Failed: {stats['failed']}")
            print(f"🔧 Errors: {stats['errors']}")
            
            # Calculate next steps
            next_entry = start_entry + stats['submitted'] + stats['failed'] + stats['errors']
            remaining = 2000 - next_entry + 1
            
            print(f"\n📋 NEXT STEPS:")
            print(f"  🎯 Next entry to process: {next_entry}")
            print(f"  📊 Remaining forms: ~{remaining}")
            print(f"  🚀 Run again with start_entry={next_entry}")
            
            return stats
            
        finally:
            print("\n🔒 Closing browser...")
            self.driver.quit()


if __name__ == "__main__":
    bot = SmartAutoBot()
    
    print("🤖 SMART AUTO-DETECTION BOT")
    print("🎯 NO MORE TYPING 'SUBMITTED'!")
    print("✅ Auto-detects successful submissions")
    print("✅ Only interrupt if there's an error")
    print("🚀 Much faster for 2000 forms!")
    
    # Start from entry 10 (since Angelina was just submitted)
    bot.run_smart_auto_batch(start_entry=10, batch_size=50)
