"""
Simple File-Based Communication System
Creates a simple text file for communication between user and bot
"""

import os
import time
import json
from datetime import datetime


class SimpleCommunication:
    """Simple file-based communication system"""
    
    def __init__(self):
        self.status_file = "bot_status.txt"
        self.response_file = "user_response.txt"
        
    def wait_for_user_response(self, person_name, id_no):
        """Wait for user response via file system"""
        
        # Create status file
        status_message = f"""
=== SAFEFORMBOT - WAITING FOR YOUR RESPONSE ===

Current Person: {person_name}
ID NO.: {id_no}
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

INSTRUCTIONS:
1. Check the browser - verify all fields are filled correctly
2. If everything looks good, manually submit the form
3. Then create a file called 'user_response.txt' with one of these:
   
   SUBMITTED - if you submitted the form
   SKIP - if you want to skip this person
   RETRY - if you want to retry filling the form

Example: Create user_response.txt with just the word: SUBMITTED

The bot is waiting for your response...
        """
        
        with open(self.status_file, 'w') as f:
            f.write(status_message.strip())
        
        print(f"\n📄 Status file created: {self.status_file}")
        print("💬 Please check the browser and create 'user_response.txt' with your response")
        print("   Options: SUBMITTED, SKIP, or RETRY")
        
        # Wait for response file
        while True:
            if os.path.exists(self.response_file):
                try:
                    with open(self.response_file, 'r') as f:
                        response = f.read().strip().upper()
                    
                    # Delete response file
                    os.remove(self.response_file)
                    
                    if response == 'SUBMITTED':
                        print("✅ User response: SUBMITTED")
                        return 'submitted'
                    elif response == 'SKIP':
                        print("⏭️  User response: SKIP")
                        return 'skip'
                    elif response == 'RETRY':
                        print("🔄 User response: RETRY")
                        return 'retry'
                    else:
                        print(f"❌ Invalid response: {response}")
                        print("   Please use: SUBMITTED, SKIP, or RETRY")
                        continue
                        
                except Exception as e:
                    print(f"❌ Error reading response: {e}")
                    continue
            
            time.sleep(1)  # Check every second
    
    def cleanup(self):
        """Clean up communication files"""
        for file in [self.status_file, self.response_file]:
            if os.path.exists(file):
                os.remove(file)


# Test the communication system
if __name__ == "__main__":
    comm = SimpleCommunication()
    
    print("Testing simple communication...")
    result = comm.wait_for_user_response("Test Person", "123456")
    print(f"Result: {result}")
    
    comm.cleanup()
