# SafeFormBot v2.0 Enhanced Requirements

# Core automation
selenium>=4.15.0
webdriver-manager>=4.0.0
pandas>=2.0.0
openpyxl>=3.1.0

# User interface and interaction
tqdm>=4.66.0
pyperclip>=1.8.2

# Web interface
Flask>=2.3.0
Flask-SocketIO>=5.3.0

# Analytics and visualization
matplotlib>=3.7.0
seaborn>=0.12.0
numpy>=1.24.0

# GUI components
tkinter-tooltip>=2.0.0

# Data validation and processing
jsonschema>=4.19.0
python-dateutil>=2.8.0

# Optional: Enhanced features
# Uncomment if you want additional features

# For advanced charts and dashboards
# plotly>=5.17.0
# dash>=2.14.0

# For database support
# sqlalchemy>=2.0.0
# sqlite3  # Built into Python

# For API integrations
# requests>=2.31.0
# urllib3>=2.0.0

# For advanced logging
# loguru>=0.7.0

# For configuration management
# pyyaml>=6.0.0
# configparser  # Built into Python

# For testing (development)
# pytest>=7.4.0
# pytest-cov>=4.1.0
# mock>=5.1.0

# For performance monitoring
# psutil>=5.9.0
# memory-profiler>=0.61.0

# For security (if handling sensitive data)
# cryptography>=41.0.0
# keyring>=24.2.0

# For advanced web scraping (if needed)
# beautifulsoup4>=4.12.0
# lxml>=4.9.0

# For email notifications (optional)
# smtplib  # Built into Python
# email-validator>=2.0.0

# For file handling
# pathlib  # Built into Python
# shutil  # Built into Python
# tempfile  # Built into Python

# For date/time handling
# datetime  # Built into Python
# time  # Built into Python

# For system operations
# os  # Built into Python
# sys  # Built into Python
# subprocess  # Built into Python
# threading  # Built into Python

# For data structures
# collections  # Built into Python
# json  # Built into Python
# csv  # Built into Python

# For regular expressions
# re  # Built into Python

# For random operations
# random  # Built into Python

# For argument parsing
# argparse  # Built into Python
