<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}SafeFormBot{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
            transition: all 0.3s;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-running { background-color: #28a745; }
        .status-stopped { background-color: #dc3545; }
        .status-idle { background-color: #ffc107; }
        
        .progress-container {
            background: rgba(255,255,255,0.1);
            border-radius: 1rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        .btn-gradient:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-robot"></i> SafeFormBot
                        </h4>
                        <small class="text-white-50">v2.0 Enhanced</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'config_page' %}active{% endif %}" href="{{ url_for('config_page') }}">
                                <i class="fas fa-cog"></i> Configuration
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'xpath_generator_page' %}active{% endif %}" href="{{ url_for('xpath_generator_page') }}">
                                <i class="fas fa-code"></i> XPath Generator
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'submissions_page' %}active{% endif %}" href="{{ url_for('submissions_page') }}">
                                <i class="fas fa-list-check"></i> Submissions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'analytics_page' %}active{% endif %}" href="{{ url_for('analytics_page') }}">
                                <i class="fas fa-chart-line"></i> Analytics
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="text-white-50">
                    
                    <!-- Bot Status -->
                    <div class="text-white-50 small px-3">
                        <div class="mb-2">
                            <strong>Bot Status:</strong>
                        </div>
                        <div id="bot-status-sidebar">
                            <span class="status-indicator status-idle"></span>
                            <span id="bot-status-text">Idle</span>
                        </div>
                        <div class="mt-2" id="bot-progress-sidebar" style="display: none;">
                            <div class="progress" style="height: 4px;">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small id="bot-progress-text">0 / 0</small>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3 pb-2 mb-3">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Socket.IO Connection -->
    <script>
        const socket = io();
        
        socket.on('connect', function() {
            console.log('Connected to SafeFormBot server');
        });
        
        socket.on('bot_status_update', function(status) {
            updateBotStatus(status);
        });
        
        socket.on('bot_error', function(data) {
            showNotification('Bot Error: ' + data.error, 'error');
        });
        
        socket.on('bot_completed', function(data) {
            showNotification(`Bot completed! Processed ${data.total_processed} submissions with ${data.errors} errors.`, 'success');
        });
        
        socket.on('submissions_updated', function(data) {
            showNotification(`${data.count} submissions ${data.action}`, 'info');
            // Refresh submissions if on submissions page
            if (window.location.pathname.includes('/submissions')) {
                location.reload();
            }
        });
        
        function updateBotStatus(status) {
            const statusElement = document.getElementById('bot-status-text');
            const indicatorElement = document.querySelector('#bot-status-sidebar .status-indicator');
            const progressContainer = document.getElementById('bot-progress-sidebar');
            const progressBar = document.querySelector('#bot-progress-sidebar .progress-bar');
            const progressText = document.getElementById('bot-progress-text');
            
            if (statusElement) {
                statusElement.textContent = status.current_action || 'Idle';
            }
            
            if (indicatorElement) {
                indicatorElement.className = 'status-indicator ' + 
                    (status.running ? 'status-running' : 'status-idle');
            }
            
            if (status.running && status.total_rows > 0) {
                const progress = (status.current_row / status.total_rows) * 100;
                progressBar.style.width = progress + '%';
                progressText.textContent = `${status.current_row} / ${status.total_rows}`;
                progressContainer.style.display = 'block';
            } else {
                progressContainer.style.display = 'none';
            }
        }
        
        function showNotification(message, type = 'info') {
            const alertClass = type === 'error' ? 'danger' : type;
            const alertHtml = `
                <div class="alert alert-${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            const container = document.querySelector('.main-content .pt-3');
            if (container) {
                container.insertAdjacentHTML('afterbegin', alertHtml);
                
                // Auto-dismiss after 5 seconds
                setTimeout(() => {
                    const alert = container.querySelector('.alert');
                    if (alert) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                }, 5000);
            }
        }
        
        // Request initial status
        socket.emit('request_status_update');
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
