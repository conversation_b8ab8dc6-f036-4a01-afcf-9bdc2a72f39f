"""
PROPER Batch Bot - Fills form, WAITS for you to submit, then moves to next
This is how it should actually work!
"""

import pandas as pd
import time
import random
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pyperclip
from selenium.webdriver.common.keys import Keys
from config_manager import ConfigManager


class ProperBatchBot:
    """Proper batch processing - fill form, wait for submission, repeat"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.config = None
        self.driver = None
        self.batch_log_file = "proper_batch_log.csv"
        
    def get_starting_point(self):
        """Get where to start based on your submissions"""
        # You said you've submitted 8 entries total
        # Let's start from entry 9 (<PERSON>)
        return 9
    
    def fill_single_form(self, row_data, entry_number):
        """Fill form for one person and verify"""
        try:
            print(f"\n📝 Filling form for: {row_data['First Name']} {row_data['Last Name']} (ID: {row_data['ID NO.']})")
            
            # Navigate to form
            form_url = self.config.get('form_url')
            self.driver.get(form_url)
            time.sleep(3)  # Wait for form to load
            
            # Fill all fields
            filled_count = 0
            field_results = []
            
            for field_name in self.config.get('fields', {}).keys():
                if field_name in row_data:
                    value = str(row_data[field_name])
                    try:
                        element = self.config_manager.find_element_with_fallbacks(self.driver, field_name)
                        if element:
                            element.clear()
                            pyperclip.copy(value)
                            element.send_keys(Keys.CONTROL, 'v')
                            time.sleep(random.uniform(0.3, 0.7))
                            filled_count += 1
                            field_results.append(f"  ✅ {field_name}: {value}")
                        else:
                            field_results.append(f"  ❌ {field_name}: Element not found")
                    except Exception as e:
                        field_results.append(f"  ❌ {field_name}: Error - {e}")
            
            # Show results
            for result in field_results:
                print(result)
            
            print(f"\n📊 Filled {filled_count}/16 fields")
            
            # Verify fields
            print("\n🔍 Verifying fields...")
            correct_count = 0
            
            for field_name in self.config.get('fields', {}).keys():
                if field_name in row_data:
                    try:
                        element = self.config_manager.find_element_with_fallbacks(self.driver, field_name)
                        if element:
                            entered_value = element.get_attribute('value')
                            expected_value = str(row_data[field_name])
                            
                            if entered_value == expected_value:
                                correct_count += 1
                                print(f"  ✅ {field_name}: '{entered_value}'")
                            else:
                                print(f"  ❌ {field_name}: Expected '{expected_value}', Got '{entered_value}'")
                    except:
                        print(f"  ❌ {field_name}: Verification failed")
            
            accuracy = (correct_count / 16) * 100
            print(f"\n📊 Verification: {correct_count}/16 correct ({accuracy:.1f}%)")
            
            return correct_count >= 15  # Allow 1 field to be slightly off
            
        except Exception as e:
            print(f"❌ Error filling form: {e}")
            return False
    
    def wait_for_submission(self, person_name, id_no, entry_number):
        """Wait for user to submit the form"""
        print(f"\n{'='*70}")
        print(f"🔍 FORM READY FOR SUBMISSION")
        print(f"{'='*70}")
        print(f"👤 Person: {person_name}")
        print(f"🆔 ID NO.: {id_no}")
        print(f"📊 Entry: {entry_number}")
        print(f"{'='*70}")
        print("📋 INSTRUCTIONS:")
        print("1. ✅ Check the browser - verify all 16 fields are correct")
        print("2. ✅ If everything looks good, manually SUBMIT the form")
        print("3. ✅ Come back here and tell me the result")
        print(f"{'='*70}")
        
        while True:
            response = input("What happened? (submitted/failed/retry/quit): ").strip().lower()
            
            if response in ['submitted', 's']:
                print("✅ Great! Form submitted successfully")
                return 'submitted'
            elif response in ['failed', 'f']:
                print("❌ Form submission failed")
                return 'failed'
            elif response in ['retry', 'r']:
                print("🔄 Will retry this form")
                return 'retry'
            elif response in ['quit', 'q']:
                print("🛑 Stopping batch processing")
                return 'quit'
            else:
                print("Please enter: submitted, failed, retry, or quit")
    
    def log_result(self, row_data, entry_number, status):
        """Log the result"""
        log_entry = {
            'Entry_Number': entry_number,
            'ID_NO': row_data.get('ID NO.', ''),
            'First_Name': row_data.get('First Name', ''),
            'Last_Name': row_data.get('Last Name', ''),
            'Status': status,
            'Timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        df = pd.DataFrame([log_entry])
        
        if os.path.exists(self.batch_log_file):
            df.to_csv(self.batch_log_file, mode='a', header=False, index=False)
        else:
            df.to_csv(self.batch_log_file, mode='w', header=True, index=False)
    
    def run_proper_batch(self, start_entry=None, batch_size=20):
        """Run proper batch processing"""
        print("🚀 PROPER BATCH BOT - ONE FORM AT A TIME")
        print("=" * 50)
        print("✅ Fills form → Waits for you → Moves to next")
        print("✅ You have time to check and submit each form")
        print("✅ No rushing, no missing forms!")
        print("=" * 50)
        
        # Load configuration
        self.config = self.config_manager.load_config()
        if not self.config:
            print("❌ Failed to load configuration")
            return
        
        # Load data
        df = pd.read_csv('input.csv')
        
        # Determine starting point
        if start_entry is None:
            start_entry = self.get_starting_point()
        
        print(f"📊 Starting from entry {start_entry}")
        print(f"📋 Will process {batch_size} entries in this session")
        
        # Confirm start
        response = input(f"\nStart processing from entry {start_entry}? (yes/no): ").strip().lower()
        if response not in ['yes', 'y']:
            print("❌ Cancelled")
            return
        
        # Initialize browser
        print("\n🚀 Starting browser...")
        options = webdriver.ChromeOptions()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        self.driver = webdriver.Chrome(
            service=ChromeService(ChromeDriverManager().install()),
            options=options
        )
        
        stats = {'submitted': 0, 'failed': 0, 'retries': 0}
        
        try:
            for i in range(batch_size):
                entry_number = start_entry + i
                
                if entry_number > len(df):
                    print(f"✅ Reached end of data at entry {entry_number}")
                    break
                
                row = df.iloc[entry_number - 1]  # Convert to 0-based index
                person_name = f"{row['First Name']} {row['Last Name']}"
                
                print(f"\n{'='*70}")
                print(f"🧑 PROCESSING ENTRY {entry_number}")
                print(f"👤 {person_name} (ID: {row['ID NO.']})")
                print(f"📊 Progress: {i+1}/{batch_size}")
                print(f"{'='*70}")
                
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        # Fill form
                        if self.fill_single_form(dict(row), entry_number):
                            # Wait for user submission
                            result = self.wait_for_submission(person_name, row['ID NO.'], entry_number)
                            
                            if result == 'submitted':
                                self.log_result(dict(row), entry_number, 'submitted')
                                stats['submitted'] += 1
                                print(f"✅ Entry {entry_number} completed successfully!")
                                break
                            elif result == 'failed':
                                self.log_result(dict(row), entry_number, 'failed')
                                stats['failed'] += 1
                                print(f"❌ Entry {entry_number} failed")
                                break
                            elif result == 'quit':
                                print("🛑 User requested to quit")
                                return stats
                            elif result == 'retry':
                                stats['retries'] += 1
                                if attempt < max_retries - 1:
                                    print(f"🔄 Retrying entry {entry_number}... (Attempt {attempt + 2}/{max_retries})")
                                    continue
                                else:
                                    print(f"❌ Max retries reached for entry {entry_number}")
                                    self.log_result(dict(row), entry_number, 'max_retries_failed')
                                    stats['failed'] += 1
                                    break
                        else:
                            print(f"❌ Form filling failed for entry {entry_number}")
                            if attempt < max_retries - 1:
                                print(f"🔄 Retrying... (Attempt {attempt + 2}/{max_retries})")
                                time.sleep(2)
                            else:
                                self.log_result(dict(row), entry_number, 'fill_failed')
                                stats['failed'] += 1
                                
                    except Exception as e:
                        print(f"❌ Error processing entry {entry_number}: {e}")
                        if attempt < max_retries - 1:
                            print(f"🔄 Retrying... (Attempt {attempt + 2}/{max_retries})")
                            time.sleep(2)
                        else:
                            self.log_result(dict(row), entry_number, f'error: {e}')
                            stats['failed'] += 1
                
                # Show current progress
                print(f"\n📊 SESSION PROGRESS:")
                print(f"  ✅ Submitted: {stats['submitted']}")
                print(f"  ❌ Failed: {stats['failed']}")
                print(f"  🔄 Retries: {stats['retries']}")
                
                # Small break between entries
                if i < batch_size - 1:
                    print(f"\n⏳ Moving to next entry in 2 seconds...")
                    time.sleep(2)
            
            print(f"\n🎉 BATCH SESSION COMPLETE!")
            print(f"✅ Successfully submitted: {stats['submitted']}")
            print(f"❌ Failed: {stats['failed']}")
            print(f"🔄 Total retries: {stats['retries']}")
            
            # Show next steps
            next_entry = start_entry + batch_size
            remaining = 2000 - (start_entry - 1 + stats['submitted'])
            print(f"\n📋 NEXT STEPS:")
            print(f"  🎯 Next entry to process: {next_entry}")
            print(f"  📊 Remaining forms: ~{remaining}")
            print(f"  🚀 Run the bot again starting from entry {next_entry}")
            
            return stats
            
        finally:
            print("\n🔒 Closing browser...")
            self.driver.quit()


if __name__ == "__main__":
    bot = ProperBatchBot()
    
    print("🎯 PROPER BATCH BOT")
    print("✅ Fills ONE form at a time")
    print("✅ WAITS for you to submit")
    print("✅ Then moves to the NEXT form")
    print("✅ No rushing, no missing forms!")
    
    # Process 20 entries at a time (manageable batch size)
    bot.run_proper_batch(start_entry=9, batch_size=20)
