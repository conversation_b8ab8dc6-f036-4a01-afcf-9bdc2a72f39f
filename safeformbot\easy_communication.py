"""
Easy Communication System - Simple Popup Windows
No file creation needed - just click buttons!
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time


class EasyCommunication:
    """Simple popup-based communication system"""
    
    def __init__(self):
        self.result = None
        self.root = None
        
    def wait_for_user_response(self, person_name, id_no):
        """Show a simple popup window for user response"""
        
        print(f"\n💬 Opening popup window for {person_name}...")
        
        # Create the popup in the main thread
        self.result = None
        
        # Create root window
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        
        # Create custom dialog
        dialog = tk.Toplevel(root)
        dialog.title("SafeFormBot - Submission Confirmation")
        dialog.geometry("500x300")
        dialog.resizable(False, False)
        
        # Make it always on top
        dialog.attributes('-topmost', True)
        dialog.grab_set()  # Make it modal
        
        # Center the window
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (300 // 2)
        dialog.geometry(f"500x300+{x}+{y}")
        
        # Title
        title_label = tk.Label(dialog, text="🤖 SafeFormBot", 
                              font=('Arial', 16, 'bold'), fg='blue')
        title_label.pack(pady=10)
        
        # Person info
        info_frame = tk.Frame(dialog, bg='lightgray', relief='raised', bd=2)
        info_frame.pack(pady=10, padx=20, fill='x')
        
        tk.Label(info_frame, text=f"👤 Person: {person_name}", 
                font=('Arial', 12, 'bold'), bg='lightgray').pack(pady=5)
        tk.Label(info_frame, text=f"🆔 ID NO.: {id_no}", 
                font=('Arial', 12), bg='lightgray').pack(pady=5)
        
        # Instructions
        instructions = """
Please check the browser window:
✅ Verify all 16 fields are filled correctly
✅ Check for any formatting issues  
✅ Ensure no fields are empty

If everything looks good, manually submit the form,
then click your response below:
        """
        
        tk.Label(dialog, text=instructions.strip(), 
                font=('Arial', 10), justify='left').pack(pady=10)
        
        # Buttons
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=20)
        
        def on_submitted():
            self.result = 'submitted'
            dialog.destroy()
            root.destroy()
        
        def on_skip():
            self.result = 'skip'
            dialog.destroy()
            root.destroy()
        
        def on_retry():
            self.result = 'retry'
            dialog.destroy()
            root.destroy()
        
        # Create buttons with colors
        submit_btn = tk.Button(button_frame, text="✅ SUBMITTED", 
                              command=on_submitted, 
                              bg='green', fg='white', 
                              font=('Arial', 12, 'bold'),
                              width=12, height=2)
        submit_btn.pack(side='left', padx=10)
        
        skip_btn = tk.Button(button_frame, text="⏭️ SKIP", 
                            command=on_skip,
                            bg='orange', fg='white',
                            font=('Arial', 12, 'bold'),
                            width=12, height=2)
        skip_btn.pack(side='left', padx=10)
        
        retry_btn = tk.Button(button_frame, text="🔄 RETRY", 
                             command=on_retry,
                             bg='blue', fg='white',
                             font=('Arial', 12, 'bold'),
                             width=12, height=2)
        retry_btn.pack(side='left', padx=10)
        
        # Handle window close
        def on_closing():
            self.result = 'skip'
            dialog.destroy()
            root.destroy()
        
        dialog.protocol("WM_DELETE_WINDOW", on_closing)
        
        # Focus on the window
        dialog.focus_force()
        
        # Run the dialog
        root.mainloop()
        
        # Return result
        if self.result == 'submitted':
            print("✅ User clicked: SUBMITTED")
            return 'submitted'
        elif self.result == 'skip':
            print("⏭️ User clicked: SKIP")
            return 'skip'
        else:
            print("🔄 User clicked: RETRY")
            return 'retry'
    
    def show_completion_message(self, stats):
        """Show completion message"""
        root = tk.Tk()
        root.withdraw()
        
        message = f"""
🎉 Processing Complete!

✅ Successfully submitted: {stats.get('submitted', 0)}
⏭️ Skipped: {stats.get('skipped', 0)}
❌ Errors: {stats.get('errors', 0)}

Total processed: {stats.get('total', 0)}
        """
        
        messagebox.showinfo("SafeFormBot Complete", message.strip())
        root.destroy()
    
    def cleanup(self):
        """Clean up any resources"""
        pass


# Test the communication system
if __name__ == "__main__":
    comm = EasyCommunication()
    
    print("Testing easy communication...")
    result = comm.wait_for_user_response("Test Person", "123456")
    print(f"Result: {result}")
    
    comm.cleanup()
