"""
Safe Submission Bot - Zero Error Tolerance
Handles dynamic field order, duplicate prevention, and manual verification
"""

import pandas as pd
import time
import random
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pyperclip
from selenium.webdriver.common.keys import Keys
from config_manager import ConfigManager


class SafeSubmissionBot:
    """Ultra-safe form submission bot with manual verification"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.config = None
        self.driver = None
        self.submitted_ids = set()
        self.current_row_data = None
        self.field_order_cache = {}
        
        # Load submitted IDs to prevent duplicates
        self.load_submitted_ids()
    
    def load_submitted_ids(self):
        """Load already submitted IDs to prevent duplicates"""
        submitted_files = ['submitted.csv', 'confirmed_submissions.csv']
        
        for file in submitted_files:
            if os.path.exists(file):
                try:
                    df = pd.read_csv(file)
                    if 'ID NO.' in df.columns:
                        ids = df['ID NO.'].astype(str).tolist()
                        self.submitted_ids.update(ids)
                        print(f"📋 Loaded {len(ids)} submitted IDs from {file}")
                except Exception as e:
                    print(f"⚠️  Warning: Could not load {file}: {e}")
        
        print(f"🛡️  Total protected IDs: {len(self.submitted_ids)}")
    
    def is_already_submitted(self, id_no):
        """Check if ID has already been submitted"""
        return str(id_no) in self.submitted_ids
    
    def detect_field_order(self):
        """Detect current field order on the form (handles dynamic ordering)"""
        if not self.driver:
            return None
        
        print("🔍 Detecting current field order...")
        field_positions = {}
        
        try:
            # Find all form fields and their positions
            form_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'geS5n')]")
            
            for i, element in enumerate(form_elements):
                try:
                    # Look for field labels
                    label_elements = element.find_elements(By.XPATH, ".//span[contains(@class, 'M7eMe')]")
                    for label_elem in label_elements:
                        label_text = label_elem.text.strip()
                        if label_text and label_text in self.config.get('fields', {}):
                            field_positions[label_text] = i
                            print(f"  📍 Position {i}: {label_text}")
                            break
                except:
                    continue
            
            return field_positions
            
        except Exception as e:
            print(f"❌ Error detecting field order: {e}")
            return None
    
    def fill_field_safely(self, field_name, value):
        """Fill a field with maximum safety checks"""
        print(f"📝 Filling '{field_name}' with '{value}'...")
        
        try:
            # Find element using config manager (with fallbacks)
            element = self.config_manager.find_element_with_fallbacks(self.driver, field_name)
            
            if not element:
                raise Exception(f"Could not find element for field '{field_name}'")
            
            # Validate data before filling
            is_valid, validation_message = self.config_manager.validate_field_data(field_name, str(value))
            if not is_valid:
                raise Exception(f"Data validation failed: {validation_message}")
            
            # Clear field completely
            element.clear()
            time.sleep(0.2)
            
            # Copy to clipboard and paste (compliance requirement)
            pyperclip.copy(str(value))
            time.sleep(0.1)
            element.send_keys(Keys.CONTROL, 'v')
            
            # Wait for field to update
            time.sleep(random.uniform(0.3, 0.7))
            
            # Verify the field was filled correctly
            entered_value = element.get_attribute('value')
            expected_value = str(value)
            
            if entered_value != expected_value:
                raise Exception(f"Verification failed: Expected '{expected_value}', got '{entered_value}'")
            
            print(f"  ✅ Success: '{entered_value}'")
            return True
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
            raise
    
    def verify_all_fields(self):
        """Verify all fields are filled correctly"""
        print("\n🔍 COMPREHENSIVE FIELD VERIFICATION")
        print("=" * 50)
        
        verification_results = {}
        all_correct = True
        
        for field_name, expected_value in self.current_row_data.items():
            if field_name in self.config.get('fields', {}):
                try:
                    element = self.config_manager.find_element_with_fallbacks(self.driver, field_name)
                    if element:
                        entered_value = element.get_attribute('value')
                        expected_str = str(expected_value)
                        
                        if entered_value == expected_str:
                            verification_results[field_name] = "✅ CORRECT"
                            print(f"✅ {field_name}: '{entered_value}'")
                        else:
                            verification_results[field_name] = f"❌ MISMATCH"
                            print(f"❌ {field_name}: Expected '{expected_str}', Got '{entered_value}'")
                            all_correct = False
                    else:
                        verification_results[field_name] = "❌ ELEMENT NOT FOUND"
                        print(f"❌ {field_name}: Element not found")
                        all_correct = False
                        
                except Exception as e:
                    verification_results[field_name] = f"❌ ERROR: {e}"
                    print(f"❌ {field_name}: {e}")
                    all_correct = False
        
        print("=" * 50)
        correct_count = sum(1 for result in verification_results.values() if result == "✅ CORRECT")
        total_count = len(verification_results)
        accuracy = (correct_count / total_count) * 100 if total_count > 0 else 0
        
        print(f"📊 VERIFICATION SUMMARY:")
        print(f"  ✅ Correct: {correct_count}/{total_count}")
        print(f"  📈 Accuracy: {accuracy:.1f}%")
        
        return all_correct, verification_results
    
    def manual_verification_prompt(self):
        """Interactive manual verification"""
        print("\n" + "="*60)
        print("🔍 MANUAL VERIFICATION REQUIRED")
        print("="*60)
        print("Please manually check EVERY field in the browser:")
        print("1. Verify all data is correct and complete")
        print("2. Check for any formatting issues")
        print("3. Ensure no fields are empty")
        print("4. Confirm data matches the source exactly")
        print("")
        print("Current person:")
        print(f"  Name: {self.current_row_data.get('First Name', '')} {self.current_row_data.get('Last Name', '')}")
        print(f"  ID NO.: {self.current_row_data.get('ID NO.', '')}")
        print("")
        
        while True:
            response = input("Is ALL data correct and ready to submit? (yes/no/retry): ").strip().lower()
            
            if response in ['yes', 'y']:
                return 'submit'
            elif response in ['no', 'n']:
                return 'skip'
            elif response in ['retry', 'r']:
                return 'retry'
            else:
                print("Please enter 'yes', 'no', or 'retry'")
    
    def process_single_entry(self, row_index, row_data):
        """Process a single entry with maximum safety"""
        self.current_row_data = dict(row_data)
        
        person_name = f"{row_data.get('First Name', '')} {row_data.get('Last Name', '')}"
        id_no = str(row_data.get('ID NO.', ''))
        
        print(f"\n{'='*60}")
        print(f"🧑 PROCESSING ENTRY {row_index}")
        print(f"👤 Name: {person_name}")
        print(f"🆔 ID NO.: {id_no}")
        print(f"{'='*60}")
        
        # Check for duplicates
        if self.is_already_submitted(id_no):
            print(f"⚠️  SKIPPING: ID {id_no} already submitted")
            return 'skipped_duplicate'
        
        try:
            # Navigate to form
            form_url = self.config.get('form_url')
            print(f"🌐 Loading form: {form_url}")
            self.driver.get(form_url)
            
            # Wait for form to load
            wait_time = self.config.get('form_settings', {}).get('wait_after_load', 3)
            print(f"⏳ Waiting {wait_time} seconds for form to load...")
            time.sleep(wait_time)
            
            # Detect field order (handles dynamic ordering)
            field_order = self.detect_field_order()
            
            # Fill all fields
            print(f"\n📝 Filling {len(self.config.get('fields', {}))} fields...")
            
            for field_name in self.config.get('fields', {}).keys():
                if field_name in row_data:
                    value = row_data[field_name]
                    self.fill_field_safely(field_name, value)
            
            # Comprehensive verification
            all_correct, verification_results = self.verify_all_fields()
            
            if not all_correct:
                print("\n❌ AUTOMATIC VERIFICATION FAILED")
                print("Some fields have errors. Manual check required.")
            
            # Manual verification (always required)
            decision = self.manual_verification_prompt()
            
            if decision == 'submit':
                print("\n📤 User approved - Ready for submission")
                print("⚠️  IMPORTANT: You must manually click SUBMIT in the browser")
                print("⚠️  Bot will NOT auto-submit for safety")
                
                input("\nPress Enter AFTER you have manually submitted the form...")
                
                # Log as submitted
                self.log_submission(row_data, 'manual_submit')
                return 'submitted'
                
            elif decision == 'retry':
                print("\n🔄 Retrying this entry...")
                return self.process_single_entry(row_index, row_data)
                
            else:
                print("\n⏭️  Skipping this entry")
                self.log_submission(row_data, 'skipped_manual')
                return 'skipped_manual'
                
        except Exception as e:
            print(f"\n❌ ERROR processing entry: {e}")
            self.log_submission(row_data, f'error: {e}')
            return 'error'
    
    def log_submission(self, row_data, status):
        """Log submission status"""
        log_entry = {
            'ID NO.': row_data.get('ID NO.', ''),
            'First Name': row_data.get('First Name', ''),
            'Last Name': row_data.get('Last Name', ''),
            'Status': status,
            'Timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Append to log file
        df_log = pd.DataFrame([log_entry])
        log_file = 'safe_submission_log.csv'
        
        if os.path.exists(log_file):
            df_log.to_csv(log_file, mode='a', header=False, index=False)
        else:
            df_log.to_csv(log_file, mode='w', header=True, index=False)
        
        # Add to submitted IDs if successful
        if status in ['manual_submit', 'submitted']:
            self.submitted_ids.add(str(row_data.get('ID NO.', '')))
    
    def run_safe_processing(self, start_row=8, end_row=None):
        """Run safe processing starting from specified row"""
        print("🚀 SafeFormBot - Ultra-Safe Processing Mode")
        print("=" * 50)
        
        # Load configuration
        self.config = self.config_manager.load_config()
        if not self.config:
            print("❌ Failed to load configuration")
            return
        
        # Load data
        if not os.path.exists('input.csv'):
            print("❌ input.csv not found")
            return
        
        df = pd.read_csv('input.csv')
        print(f"📊 Loaded {len(df)} total entries")
        
        # Determine processing range (convert to 0-based indexing)
        if end_row:
            df_to_process = df.iloc[start_row-1:end_row]  # start_row-1 for 0-based indexing
        else:
            df_to_process = df.iloc[start_row-1:]
        
        print(f"🎯 Processing entries {start_row} to {start_row + len(df_to_process) - 1}")
        print(f"📋 Total entries to process: {len(df_to_process)}")
        
        # Confirm start
        first_person = df_to_process.iloc[0]
        print(f"\n👤 Starting with: {first_person['First Name']} {first_person['Last Name']} (ID: {first_person['ID NO.']})")
        
        confirm = input("\nProceed with safe processing? (yes/no): ").strip().lower()
        if confirm not in ['yes', 'y']:
            print("❌ Processing cancelled")
            return
        
        # Initialize browser
        print("\n🚀 Starting browser...")
        options = webdriver.ChromeOptions()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        # Keep browser visible for manual verification
        
        self.driver = webdriver.Chrome(
            service=ChromeService(ChromeDriverManager().install()),
            options=options
        )
        
        try:
            results = {'submitted': 0, 'skipped': 0, 'errors': 0}
            
            for idx, (_, row) in enumerate(df_to_process.iterrows(), start=start_row):
                result = self.process_single_entry(idx, row)
                
                if result == 'submitted':
                    results['submitted'] += 1
                elif result.startswith('skipped'):
                    results['skipped'] += 1
                else:
                    results['errors'] += 1
                
                print(f"\n📊 Progress: {idx - start_row + 1}/{len(df_to_process)}")
                print(f"✅ Submitted: {results['submitted']}")
                print(f"⏭️  Skipped: {results['skipped']}")
                print(f"❌ Errors: {results['errors']}")
                
                # Ask to continue
                if idx < start_row + len(df_to_process) - 1:
                    continue_processing = input("\nContinue to next entry? (yes/no): ").strip().lower()
                    if continue_processing not in ['yes', 'y']:
                        print("🛑 Processing stopped by user")
                        break
            
            print(f"\n🎉 PROCESSING COMPLETE")
            print(f"✅ Successfully submitted: {results['submitted']}")
            print(f"⏭️  Skipped: {results['skipped']}")
            print(f"❌ Errors: {results['errors']}")
            
        finally:
            print("\n🔒 Closing browser...")
            self.driver.quit()


if __name__ == "__main__":
    bot = SafeSubmissionBot()

    # Test with just Numbers E. Dominique (row 7, index 6)
    print("🧪 TESTING MODE: Processing only Numbers E. Dominique")
    bot.run_safe_processing(start_row=7, end_row=7)  # Numbers E. Dominique for testing
