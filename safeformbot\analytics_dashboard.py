"""
Performance Monitoring & Analytics Dashboard for SafeFormBot
Real-time tracking of accuracy rates, error patterns, and performance metrics
"""

import pandas as pd
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np


class PerformanceAnalyzer:
    """Analyzes SafeFormBot performance and generates metrics"""
    
    def __init__(self):
        self.submission_log_file = "submission_log.csv"
        self.errors_file = "errors.csv"
        self.pending_file = "pending_submissions.csv"
        self.confirmed_file = "confirmed_submissions.csv"
        self.rejected_file = "rejected_submissions.csv"
        
        # Error classification based on requirements
        self.minor_error_patterns = [
            "timeout", "slow response", "network delay", "minor validation",
            "formatting", "whitespace", "case sensitivity"
        ]
        
        self.major_error_patterns = [
            "xpath not found", "element not found", "form submission failed",
            "data mismatch", "validation failed", "critical error", "crash"
        ]
    
    def calculate_accuracy_metrics(self) -> Dict:
        """Calculate accuracy metrics based on Gold/VIP plan requirements"""
        metrics = {
            'total_submissions': 0,
            'successful_submissions': 0,
            'failed_submissions': 0,
            'minor_errors': 0,
            'major_errors': 0,
            'accuracy_percentage': 0.0,
            'error_rate': 0.0,
            'gold_plan_status': 'UNKNOWN',
            'vip_plan_status': 'UNKNOWN'
        }
        
        # Count successful submissions
        if os.path.exists(self.confirmed_file):
            df_confirmed = pd.read_csv(self.confirmed_file)
            metrics['successful_submissions'] = len(df_confirmed)
        
        # Count failed submissions and categorize errors
        if os.path.exists(self.errors_file):
            df_errors = pd.read_csv(self.errors_file)
            metrics['failed_submissions'] = len(df_errors)
            
            # Categorize errors
            for _, error_row in df_errors.iterrows():
                error_message = str(error_row.get('Error', '')).lower()
                
                is_major = any(pattern in error_message for pattern in self.major_error_patterns)
                if is_major:
                    metrics['major_errors'] += 1
                else:
                    metrics['minor_errors'] += 1
        
        metrics['total_submissions'] = metrics['successful_submissions'] + metrics['failed_submissions']
        
        # Calculate accuracy percentage
        if metrics['total_submissions'] > 0:
            # Accuracy calculation: (successful - error_deductions) / total
            minor_deduction = metrics['minor_errors'] * 0.02  # 2% per minor error
            major_deduction = metrics['major_errors'] * 0.04  # 4% per major error
            
            base_accuracy = metrics['successful_submissions'] / metrics['total_submissions']
            total_deduction = (minor_deduction + major_deduction) / metrics['total_submissions']
            
            metrics['accuracy_percentage'] = max(0, (base_accuracy - total_deduction) * 100)
            metrics['error_rate'] = (metrics['failed_submissions'] / metrics['total_submissions']) * 100
        
        # Determine plan status
        metrics['gold_plan_status'] = self._evaluate_gold_plan_status(metrics)
        metrics['vip_plan_status'] = self._evaluate_vip_plan_status(metrics)
        
        return metrics
    
    def _evaluate_gold_plan_status(self, metrics: Dict) -> str:
        """Evaluate Gold Plan status based on requirements"""
        # Gold Plan limits: Max 10 minor errors, Max 5 major errors, 80% accuracy threshold
        if metrics['minor_errors'] > 10:
            return 'FAILED - Too many minor errors (>10)'
        if metrics['major_errors'] > 5:
            return 'FAILED - Too many major errors (>5)'
        if metrics['accuracy_percentage'] < 80:
            return 'FAILED - Accuracy below 80%'
        if metrics['error_rate'] > 20:
            return 'FAILED - Error rate above 20%'
        
        return 'PASSED'
    
    def _evaluate_vip_plan_status(self, metrics: Dict) -> str:
        """Evaluate VIP Plan status based on requirements"""
        # VIP Plan limits: Max 15 minor errors, Max 7 major errors, 70% accuracy threshold
        if metrics['minor_errors'] > 15:
            return 'FAILED - Too many minor errors (>15)'
        if metrics['major_errors'] > 7:
            return 'FAILED - Too many major errors (>7)'
        if metrics['accuracy_percentage'] < 70:
            return 'FAILED - Accuracy below 70%'
        if metrics['error_rate'] > 30:
            return 'FAILED - Error rate above 30%'
        
        return 'PASSED'
    
    def analyze_error_patterns(self) -> Dict:
        """Analyze error patterns and identify common failure points"""
        error_analysis = {
            'most_common_errors': [],
            'error_by_field': defaultdict(int),
            'error_by_time': defaultdict(int),
            'error_trends': []
        }
        
        if not os.path.exists(self.errors_file):
            return error_analysis
        
        df_errors = pd.read_csv(self.errors_file)
        
        # Most common error messages
        error_messages = df_errors['Error'].value_counts().head(10)
        error_analysis['most_common_errors'] = [
            {'error': error, 'count': count} 
            for error, count in error_messages.items()
        ]
        
        # Errors by field (if field information is available)
        if 'Field' in df_errors.columns:
            field_errors = df_errors['Field'].value_counts()
            error_analysis['error_by_field'] = dict(field_errors)
        
        # Errors by time (hourly distribution)
        if 'Timestamp' in df_errors.columns:
            df_errors['Timestamp'] = pd.to_datetime(df_errors['Timestamp'])
            df_errors['Hour'] = df_errors['Timestamp'].dt.hour
            hourly_errors = df_errors['Hour'].value_counts().sort_index()
            error_analysis['error_by_time'] = dict(hourly_errors)
        
        return error_analysis
    
    def calculate_performance_metrics(self) -> Dict:
        """Calculate performance metrics like speed and throughput"""
        performance = {
            'avg_submission_time': 0.0,
            'submissions_per_hour': 0.0,
            'peak_performance_hour': 0,
            'slowest_fields': [],
            'fastest_fields': []
        }
        
        if not os.path.exists(self.submission_log_file):
            return performance
        
        df_submissions = pd.read_csv(self.submission_log_file)
        
        if 'Timestamp' in df_submissions.columns:
            df_submissions['Timestamp'] = pd.to_datetime(df_submissions['Timestamp'])
            
            # Calculate submissions per hour
            df_submissions['Hour'] = df_submissions['Timestamp'].dt.hour
            hourly_submissions = df_submissions['Hour'].value_counts()
            
            if not hourly_submissions.empty:
                performance['submissions_per_hour'] = hourly_submissions.mean()
                performance['peak_performance_hour'] = hourly_submissions.idxmax()
        
        return performance
    
    def generate_quality_report(self) -> str:
        """Generate a comprehensive quality report"""
        metrics = self.calculate_accuracy_metrics()
        error_analysis = self.analyze_error_patterns()
        performance = self.calculate_performance_metrics()
        
        report = []
        report.append("=== SAFEFORMBOT QUALITY CONTROL REPORT ===")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Accuracy Metrics
        report.append("ACCURACY METRICS:")
        report.append("-" * 40)
        report.append(f"Total Submissions: {metrics['total_submissions']}")
        report.append(f"Successful: {metrics['successful_submissions']}")
        report.append(f"Failed: {metrics['failed_submissions']}")
        report.append(f"Minor Errors: {metrics['minor_errors']}")
        report.append(f"Major Errors: {metrics['major_errors']}")
        report.append(f"Accuracy: {metrics['accuracy_percentage']:.2f}%")
        report.append(f"Error Rate: {metrics['error_rate']:.2f}%")
        report.append("")
        
        # Plan Status
        report.append("PLAN STATUS EVALUATION:")
        report.append("-" * 40)
        report.append(f"Gold Plan Status: {metrics['gold_plan_status']}")
        report.append(f"VIP Plan Status: {metrics['vip_plan_status']}")
        report.append("")
        
        # Error Analysis
        if error_analysis['most_common_errors']:
            report.append("MOST COMMON ERRORS:")
            report.append("-" * 40)
            for error_info in error_analysis['most_common_errors'][:5]:
                report.append(f"  {error_info['count']}x: {error_info['error']}")
            report.append("")
        
        # Performance Metrics
        report.append("PERFORMANCE METRICS:")
        report.append("-" * 40)
        report.append(f"Submissions per Hour: {performance['submissions_per_hour']:.1f}")
        report.append(f"Peak Performance Hour: {performance['peak_performance_hour']}:00")
        report.append("")
        
        # Recommendations
        report.append("RECOMMENDATIONS:")
        report.append("-" * 40)
        
        if metrics['accuracy_percentage'] < 80:
            report.append("⚠️  CRITICAL: Accuracy below Gold Plan threshold (80%)")
            report.append("   - Review and fix major error sources")
            report.append("   - Validate XPath selectors")
            report.append("   - Check data quality")
        
        if metrics['minor_errors'] > 10:
            report.append("⚠️  WARNING: Minor errors exceed Gold Plan limit (10)")
            report.append("   - Optimize wait times and timeouts")
            report.append("   - Improve error handling")
        
        if metrics['major_errors'] > 5:
            report.append("🚨 CRITICAL: Major errors exceed Gold Plan limit (5)")
            report.append("   - Immediate review of form selectors required")
            report.append("   - Check for website changes")
        
        if metrics['accuracy_percentage'] >= 90:
            report.append("✅ EXCELLENT: High accuracy achieved")
            report.append("   - Consider increasing processing speed")
            report.append("   - Monitor for consistency")
        
        return "\n".join(report)
    
    def export_analytics_data(self, filename: str = None) -> str:
        """Export analytics data to JSON file"""
        if not filename:
            filename = f"analytics_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        analytics_data = {
            'timestamp': datetime.now().isoformat(),
            'accuracy_metrics': self.calculate_accuracy_metrics(),
            'error_analysis': self.analyze_error_patterns(),
            'performance_metrics': self.calculate_performance_metrics()
        }
        
        with open(filename, 'w') as f:
            json.dump(analytics_data, f, indent=2, default=str)
        
        return filename


class AnalyticsDashboard:
    """GUI Dashboard for performance monitoring"""
    
    def __init__(self):
        self.analyzer = PerformanceAnalyzer()
        self.root = None
        self.refresh_interval = 30000  # 30 seconds
        
    def start_dashboard(self):
        """Start the analytics dashboard"""
        self.root = tk.Tk()
        self.root.title("SafeFormBot - Analytics Dashboard")
        self.root.geometry("1200x800")
        
        self._create_widgets()
        self._update_dashboard()
        
        # Auto-refresh
        self.root.after(self.refresh_interval, self._auto_refresh)
        
        self.root.mainloop()
    
    def _create_widgets(self):
        """Create dashboard widgets"""
        # Main notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Overview Tab
        overview_frame = ttk.Frame(notebook)
        notebook.add(overview_frame, text="Overview")
        self._create_overview_tab(overview_frame)
        
        # Error Analysis Tab
        error_frame = ttk.Frame(notebook)
        notebook.add(error_frame, text="Error Analysis")
        self._create_error_tab(error_frame)
        
        # Performance Tab
        performance_frame = ttk.Frame(notebook)
        notebook.add(performance_frame, text="Performance")
        self._create_performance_tab(performance_frame)
    
    def _create_overview_tab(self, parent):
        """Create overview tab with key metrics"""
        # Metrics frame
        metrics_frame = ttk.LabelFrame(parent, text="Key Metrics", padding="10")
        metrics_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Create metric labels
        self.accuracy_label = ttk.Label(metrics_frame, text="Accuracy: Loading...", font=('Arial', 12, 'bold'))
        self.accuracy_label.grid(row=0, column=0, padx=10, pady=5)
        
        self.error_rate_label = ttk.Label(metrics_frame, text="Error Rate: Loading...", font=('Arial', 12))
        self.error_rate_label.grid(row=0, column=1, padx=10, pady=5)
        
        self.total_submissions_label = ttk.Label(metrics_frame, text="Total: Loading...", font=('Arial', 12))
        self.total_submissions_label.grid(row=0, column=2, padx=10, pady=5)
        
        # Plan status frame
        status_frame = ttk.LabelFrame(parent, text="Plan Status", padding="10")
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.gold_status_label = ttk.Label(status_frame, text="Gold Plan: Loading...", font=('Arial', 11))
        self.gold_status_label.grid(row=0, column=0, padx=10, pady=5)
        
        self.vip_status_label = ttk.Label(status_frame, text="VIP Plan: Loading...", font=('Arial', 11))
        self.vip_status_label.grid(row=0, column=1, padx=10, pady=5)
        
        # Quality report frame
        report_frame = ttk.LabelFrame(parent, text="Quality Report", padding="10")
        report_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Text widget with scrollbar
        text_frame = ttk.Frame(report_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.report_text = tk.Text(text_frame, wrap=tk.WORD, font=('Courier', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.report_text.yview)
        self.report_text.configure(yscrollcommand=scrollbar.set)
        
        self.report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Buttons
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(button_frame, text="Refresh", command=self._update_dashboard).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Export Report", command=self._export_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Export Analytics", command=self._export_analytics).pack(side=tk.LEFT, padx=5)
    
    def _create_error_tab(self, parent):
        """Create error analysis tab"""
        # Error summary
        summary_frame = ttk.LabelFrame(parent, text="Error Summary", padding="10")
        summary_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.minor_errors_label = ttk.Label(summary_frame, text="Minor Errors: 0")
        self.minor_errors_label.grid(row=0, column=0, padx=10)
        
        self.major_errors_label = ttk.Label(summary_frame, text="Major Errors: 0")
        self.major_errors_label.grid(row=0, column=1, padx=10)
        
        # Error list
        list_frame = ttk.LabelFrame(parent, text="Common Errors", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Treeview for errors
        self.error_tree = ttk.Treeview(list_frame, columns=('Count', 'Error'), show='headings')
        self.error_tree.heading('Count', text='Count')
        self.error_tree.heading('Error', text='Error Message')
        self.error_tree.column('Count', width=80)
        self.error_tree.column('Error', width=400)
        
        error_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.error_tree.yview)
        self.error_tree.configure(yscrollcommand=error_scrollbar.set)
        
        self.error_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        error_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def _create_performance_tab(self, parent):
        """Create performance monitoring tab"""
        perf_frame = ttk.LabelFrame(parent, text="Performance Metrics", padding="10")
        perf_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.submissions_per_hour_label = ttk.Label(perf_frame, text="Submissions/Hour: 0")
        self.submissions_per_hour_label.grid(row=0, column=0, padx=10)
        
        self.peak_hour_label = ttk.Label(perf_frame, text="Peak Hour: N/A")
        self.peak_hour_label.grid(row=0, column=1, padx=10)
    
    def _update_dashboard(self):
        """Update dashboard with latest data"""
        try:
            # Get latest metrics
            metrics = self.analyzer.calculate_accuracy_metrics()
            error_analysis = self.analyzer.analyze_error_patterns()
            performance = self.analyzer.calculate_performance_metrics()
            
            # Update overview metrics
            self.accuracy_label.config(text=f"Accuracy: {metrics['accuracy_percentage']:.1f}%")
            self.error_rate_label.config(text=f"Error Rate: {metrics['error_rate']:.1f}%")
            self.total_submissions_label.config(text=f"Total: {metrics['total_submissions']}")
            
            # Update plan status with colors
            gold_status = metrics['gold_plan_status']
            gold_color = 'green' if gold_status == 'PASSED' else 'red'
            self.gold_status_label.config(text=f"Gold Plan: {gold_status}", foreground=gold_color)
            
            vip_status = metrics['vip_plan_status']
            vip_color = 'green' if vip_status == 'PASSED' else 'red'
            self.vip_status_label.config(text=f"VIP Plan: {vip_status}", foreground=vip_color)
            
            # Update quality report
            report = self.analyzer.generate_quality_report()
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)
            
            # Update error analysis
            self.minor_errors_label.config(text=f"Minor Errors: {metrics['minor_errors']}")
            self.major_errors_label.config(text=f"Major Errors: {metrics['major_errors']}")
            
            # Update error tree
            for item in self.error_tree.get_children():
                self.error_tree.delete(item)
            
            for error_info in error_analysis['most_common_errors'][:10]:
                self.error_tree.insert('', tk.END, values=(error_info['count'], error_info['error']))
            
            # Update performance metrics
            self.submissions_per_hour_label.config(text=f"Submissions/Hour: {performance['submissions_per_hour']:.1f}")
            self.peak_hour_label.config(text=f"Peak Hour: {performance['peak_performance_hour']}:00")
            
        except Exception as e:
            print(f"Error updating dashboard: {e}")
    
    def _auto_refresh(self):
        """Auto-refresh dashboard"""
        self._update_dashboard()
        self.root.after(self.refresh_interval, self._auto_refresh)
    
    def _export_report(self):
        """Export quality report"""
        report = self.analyzer.generate_quality_report()
        filename = f"quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(filename, 'w') as f:
            f.write(report)
        
        tk.messagebox.showinfo("Export Complete", f"Quality report exported to: {filename}")
    
    def _export_analytics(self):
        """Export analytics data"""
        filename = self.analyzer.export_analytics_data()
        tk.messagebox.showinfo("Export Complete", f"Analytics data exported to: {filename}")


def start_analytics_dashboard():
    """Start the analytics dashboard"""
    dashboard = AnalyticsDashboard()
    dashboard.start_dashboard()


if __name__ == "__main__":
    # Start the dashboard
    start_analytics_dashboard()
