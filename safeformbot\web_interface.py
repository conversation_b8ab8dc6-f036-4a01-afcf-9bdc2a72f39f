"""
Modern Web Interface for SafeFormBot
Flask-based web application for configuration, monitoring, and control
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_socketio import SocketIO, emit
import json
import os
import threading
import time
from datetime import datetime
from config_manager import ConfigManager
from submission_manager import SubmissionManager
from analytics_dashboard import PerformanceAnalyzer
from xpath_generator import XPathGenerator
import pandas as pd


app = Flask(__name__)
app.secret_key = 'safeformbot_secret_key_2025'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global instances
config_manager = ConfigManager()
submission_manager = SubmissionManager()
performance_analyzer = PerformanceAnalyzer()
xpath_generator = XPathGenerator()

# Bot status
bot_status = {
    'running': False,
    'current_row': 0,
    'total_rows': 0,
    'current_action': 'Idle',
    'start_time': None,
    'errors': []
}


@app.route('/')
def index():
    """Main dashboard"""
    try:
        # Get current statistics
        stats = submission_manager.get_submission_stats()
        metrics = performance_analyzer.calculate_accuracy_metrics()
        
        return render_template('dashboard.html', 
                             stats=stats, 
                             metrics=metrics,
                             bot_status=bot_status)
    except Exception as e:
        flash(f'Error loading dashboard: {e}', 'error')
        return render_template('dashboard.html', 
                             stats={}, 
                             metrics={},
                             bot_status=bot_status)


@app.route('/config')
def config_page():
    """Configuration management page"""
    try:
        config = config_manager.load_config()
        return render_template('config.html', config=config)
    except Exception as e:
        flash(f'Error loading configuration: {e}', 'error')
        return render_template('config.html', config={})


@app.route('/config/save', methods=['POST'])
def save_config():
    """Save configuration"""
    try:
        config_data = request.json
        config_manager.save_config(config_data)
        return jsonify({'success': True, 'message': 'Configuration saved successfully'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/config/validate', methods=['POST'])
def validate_config():
    """Validate form configuration"""
    try:
        form_url = request.json.get('form_url')
        results = config_manager.validate_selectors(form_url, headless=True)
        return jsonify({'success': True, 'results': results})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/xpath-generator')
def xpath_generator_page():
    """XPath generator tool page"""
    return render_template('xpath_generator.html')


@app.route('/xpath/generate', methods=['POST'])
def generate_xpath():
    """Generate XPath selectors"""
    try:
        data = request.json
        form_url = data.get('form_url')
        field_name = data.get('field_name')
        text_hints = data.get('text_hints', [field_name])
        
        # Start browser session if not already started
        if not hasattr(xpath_generator, 'driver') or not xpath_generator.driver:
            xpath_generator.start_browser(form_url, headless=True)
        
        # Find best selector
        result = xpath_generator.find_best_selector(field_name, text_hints)
        
        return jsonify({'success': True, 'result': result})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/xpath/test', methods=['POST'])
def test_xpath():
    """Test XPath selector"""
    try:
        data = request.json
        selector = data.get('selector')
        selector_type = data.get('type', 'xpath')
        
        if selector_type == 'xpath':
            result = xpath_generator.test_xpath(selector)
        else:
            result = xpath_generator.test_css_selector(selector)
        
        return jsonify({'success': True, 'result': result})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/submissions')
def submissions_page():
    """Submissions management page"""
    try:
        pending_df = submission_manager.get_pending_submissions()
        stats = submission_manager.get_submission_stats()
        
        pending_submissions = pending_df.to_dict('records') if not pending_df.empty else []
        
        return render_template('submissions.html', 
                             submissions=pending_submissions,
                             stats=stats)
    except Exception as e:
        flash(f'Error loading submissions: {e}', 'error')
        return render_template('submissions.html', submissions=[], stats={})


@app.route('/submissions/confirm', methods=['POST'])
def confirm_submissions():
    """Confirm selected submissions"""
    try:
        submission_ids = request.json.get('submission_ids', [])
        results = submission_manager.batch_confirm_submissions(submission_ids)
        
        # Emit real-time update
        socketio.emit('submissions_updated', {'action': 'confirmed', 'count': len(results['success'])})
        
        return jsonify({'success': True, 'results': results})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/submissions/reject', methods=['POST'])
def reject_submissions():
    """Reject selected submissions"""
    try:
        data = request.json
        submission_ids = data.get('submission_ids', [])
        reason = data.get('reason', '')
        
        success_count = 0
        for submission_id in submission_ids:
            if submission_manager.reject_submission(submission_id, reason):
                success_count += 1
        
        # Emit real-time update
        socketio.emit('submissions_updated', {'action': 'rejected', 'count': success_count})
        
        return jsonify({'success': True, 'count': success_count})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/analytics')
def analytics_page():
    """Analytics and performance page"""
    try:
        metrics = performance_analyzer.calculate_accuracy_metrics()
        error_analysis = performance_analyzer.analyze_error_patterns()
        performance = performance_analyzer.calculate_performance_metrics()
        
        return render_template('analytics.html',
                             metrics=metrics,
                             error_analysis=error_analysis,
                             performance=performance)
    except Exception as e:
        flash(f'Error loading analytics: {e}', 'error')
        return render_template('analytics.html',
                             metrics={},
                             error_analysis={},
                             performance={})


@app.route('/analytics/report')
def generate_report():
    """Generate and download quality report"""
    try:
        report = performance_analyzer.generate_quality_report()
        filename = f"quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(filename, 'w') as f:
            f.write(report)
        
        return jsonify({'success': True, 'filename': filename, 'report': report})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/bot/start', methods=['POST'])
def start_bot():
    """Start the form submission bot"""
    try:
        data = request.json
        start_row = data.get('start_row', 1)
        end_row = data.get('end_row', None)
        
        if bot_status['running']:
            return jsonify({'success': False, 'message': 'Bot is already running'})
        
        # Start bot in background thread
        bot_thread = threading.Thread(target=run_bot_background, args=(start_row, end_row))
        bot_thread.daemon = True
        bot_thread.start()
        
        return jsonify({'success': True, 'message': 'Bot started successfully'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/bot/stop', methods=['POST'])
def stop_bot():
    """Stop the form submission bot"""
    try:
        bot_status['running'] = False
        bot_status['current_action'] = 'Stopping...'
        
        # Emit real-time update
        socketio.emit('bot_status_update', bot_status)
        
        return jsonify({'success': True, 'message': 'Bot stop requested'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/bot/status')
def get_bot_status():
    """Get current bot status"""
    return jsonify(bot_status)


def run_bot_background(start_row, end_row):
    """Run the bot in background thread"""
    global bot_status
    
    try:
        bot_status['running'] = True
        bot_status['start_time'] = datetime.now().isoformat()
        bot_status['current_action'] = 'Initializing...'
        bot_status['errors'] = []
        
        # Emit status update
        socketio.emit('bot_status_update', bot_status)
        
        # Load data
        if not os.path.exists('input.csv'):
            raise Exception('input.csv not found')
        
        df = pd.read_csv('input.csv')
        
        if end_row:
            df = df.iloc[start_row-1:end_row]
        else:
            df = df.iloc[start_row-1:]
        
        bot_status['total_rows'] = len(df)
        bot_status['current_action'] = 'Processing submissions...'
        
        # Process each row
        for index, row in df.iterrows():
            if not bot_status['running']:
                break
            
            bot_status['current_row'] = index + 1
            bot_status['current_action'] = f"Processing {row.get('First Name', 'Unknown')} {row.get('Last Name', '')}"
            
            # Emit progress update
            socketio.emit('bot_status_update', bot_status)
            
            try:
                # Simulate form submission (replace with actual bot logic)
                time.sleep(2)  # Simulate processing time
                
                # Add to pending submissions
                timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                submission_manager.add_pending_submission(dict(row), timestamp)
                
                # Emit submission update
                socketio.emit('new_submission', {
                    'name': f"{row.get('First Name', '')} {row.get('Last Name', '')}",
                    'id_no': row.get('ID NO.', ''),
                    'timestamp': timestamp
                })
                
            except Exception as e:
                error_msg = f"Error processing row {index + 1}: {str(e)}"
                bot_status['errors'].append(error_msg)
                socketio.emit('bot_error', {'error': error_msg})
        
        bot_status['running'] = False
        bot_status['current_action'] = 'Completed'
        
        # Final status update
        socketio.emit('bot_status_update', bot_status)
        socketio.emit('bot_completed', {
            'total_processed': bot_status['current_row'],
            'errors': len(bot_status['errors'])
        })
        
    except Exception as e:
        bot_status['running'] = False
        bot_status['current_action'] = f'Error: {str(e)}'
        bot_status['errors'].append(str(e))
        
        socketio.emit('bot_status_update', bot_status)
        socketio.emit('bot_error', {'error': str(e)})


# WebSocket events
@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    emit('connected', {'message': 'Connected to SafeFormBot'})


@socketio.on('request_status_update')
def handle_status_request():
    """Handle status update request"""
    emit('bot_status_update', bot_status)


# Template filters
@app.template_filter('datetime')
def datetime_filter(timestamp):
    """Format datetime for templates"""
    if isinstance(timestamp, str):
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return timestamp
    return str(timestamp)


if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    print("Starting SafeFormBot Web Interface...")
    print("Access the dashboard at: http://localhost:5000")
    
    # Run the Flask app with SocketIO
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
