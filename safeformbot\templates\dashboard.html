{% extends "base.html" %}

{% block title %}Dashboard - SafeFormBot{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-gradient" onclick="startBot()">
                <i class="fas fa-play"></i> Start Bot
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="stopBot()">
                <i class="fas fa-stop"></i> Stop Bot
            </button>
        </div>
    </div>
</div>

<!-- Key Metrics Row -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Accuracy Rate</div>
                        <div class="h5 mb-0 font-weight-bold">{{ "%.1f"|format(metrics.accuracy_percentage or 0) }}%</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-bullseye fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Submissions</div>
                        <div class="h5 mb-0 font-weight-bold">{{ metrics.total_submissions or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Pending</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.pending or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Error Rate</div>
                        <div class="h5 mb-0 font-weight-bold">{{ "%.1f"|format(metrics.error_rate or 0) }}%</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bot Status and Control -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-robot"></i> Bot Status & Control</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Start Row:</label>
                            <input type="number" class="form-control" id="start-row" value="1" min="1">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">End Row (optional):</label>
                            <input type="number" class="form-control" id="end-row" min="1" placeholder="Leave empty for all">
                        </div>
                    </div>
                </div>
                
                <div id="bot-status-display" class="progress-container" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span id="current-action">Idle</span>
                        <span id="progress-text">0 / 0</span>
                    </div>
                    <div class="progress">
                        <div id="main-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">Started: <span id="start-time">-</span></small>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-gradient me-2" onclick="startBot()">
                        <i class="fas fa-play"></i> Start Processing
                    </button>
                    <button class="btn btn-outline-danger me-2" onclick="stopBot()">
                        <i class="fas fa-stop"></i> Stop
                    </button>
                    <button class="btn btn-outline-secondary" onclick="refreshStatus()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-chart-pie"></i> Plan Status</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Gold Plan:</span>
                        <span class="badge {% if metrics.gold_plan_status == 'PASSED' %}bg-success{% else %}bg-danger{% endif %}">
                            {{ metrics.gold_plan_status or 'UNKNOWN' }}
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>VIP Plan:</span>
                        <span class="badge {% if metrics.vip_plan_status == 'PASSED' %}bg-success{% else %}bg-danger{% endif %}">
                            {{ metrics.vip_plan_status or 'UNKNOWN' }}
                        </span>
                    </div>
                </div>
                
                <hr>
                
                <div class="small text-muted">
                    <div class="mb-1">Minor Errors: {{ metrics.minor_errors or 0 }}</div>
                    <div class="mb-1">Major Errors: {{ metrics.major_errors or 0 }}</div>
                    <div>Success Rate: {{ "%.1f"|format(((metrics.successful_submissions or 0) / (metrics.total_submissions or 1)) * 100) }}%</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-history"></i> Recent Submissions</h5>
            </div>
            <div class="card-body">
                <div id="recent-submissions">
                    <div class="text-center text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>No recent submissions</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-exclamation-circle"></i> Recent Errors</h5>
            </div>
            <div class="card-body">
                <div id="recent-errors">
                    <div class="text-center text-muted">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <p>No recent errors</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Bot control functions
    function startBot() {
        const startRow = document.getElementById('start-row').value;
        const endRow = document.getElementById('end-row').value;
        
        const data = {
            start_row: parseInt(startRow) || 1,
            end_row: endRow ? parseInt(endRow) : null
        };
        
        fetch('/bot/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire('Success', 'Bot started successfully!', 'success');
            } else {
                Swal.fire('Error', data.message, 'error');
            }
        })
        .catch(error => {
            Swal.fire('Error', 'Failed to start bot: ' + error, 'error');
        });
    }
    
    function stopBot() {
        fetch('/bot/stop', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire('Success', 'Bot stop requested', 'info');
            } else {
                Swal.fire('Error', data.message, 'error');
            }
        })
        .catch(error => {
            Swal.fire('Error', 'Failed to stop bot: ' + error, 'error');
        });
    }
    
    function refreshStatus() {
        fetch('/bot/status')
        .then(response => response.json())
        .then(status => {
            updateBotStatus(status);
        })
        .catch(error => {
            console.error('Failed to refresh status:', error);
        });
    }
    
    // Override the global updateBotStatus function for dashboard-specific updates
    function updateBotStatus(status) {
        // Update sidebar status (call parent function)
        const statusElement = document.getElementById('bot-status-text');
        const indicatorElement = document.querySelector('#bot-status-sidebar .status-indicator');
        const progressContainer = document.getElementById('bot-progress-sidebar');
        const progressBar = document.querySelector('#bot-progress-sidebar .progress-bar');
        const progressText = document.getElementById('bot-progress-text');
        
        if (statusElement) {
            statusElement.textContent = status.current_action || 'Idle';
        }
        
        if (indicatorElement) {
            indicatorElement.className = 'status-indicator ' + 
                (status.running ? 'status-running' : 'status-idle');
        }
        
        if (status.running && status.total_rows > 0) {
            const progress = (status.current_row / status.total_rows) * 100;
            progressBar.style.width = progress + '%';
            progressText.textContent = `${status.current_row} / ${status.total_rows}`;
            progressContainer.style.display = 'block';
        } else {
            progressContainer.style.display = 'none';
        }
        
        // Update main dashboard display
        const mainProgressBar = document.getElementById('main-progress-bar');
        const mainProgressText = document.getElementById('progress-text');
        const currentActionElement = document.getElementById('current-action');
        const startTimeElement = document.getElementById('start-time');
        const statusDisplay = document.getElementById('bot-status-display');
        
        if (currentActionElement) {
            currentActionElement.textContent = status.current_action || 'Idle';
        }
        
        if (status.running) {
            statusDisplay.style.display = 'block';
            
            if (status.total_rows > 0) {
                const progress = (status.current_row / status.total_rows) * 100;
                mainProgressBar.style.width = progress + '%';
                mainProgressText.textContent = `${status.current_row} / ${status.total_rows}`;
            }
            
            if (status.start_time && startTimeElement) {
                const startTime = new Date(status.start_time);
                startTimeElement.textContent = startTime.toLocaleString();
            }
        } else {
            if (!status.current_action || status.current_action === 'Idle') {
                statusDisplay.style.display = 'none';
            }
        }
    }
    
    // Socket.IO event handlers for dashboard
    socket.on('new_submission', function(data) {
        addRecentSubmission(data);
    });
    
    function addRecentSubmission(data) {
        const container = document.getElementById('recent-submissions');
        const submissionHtml = `
            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                <div>
                    <strong>${data.name}</strong><br>
                    <small class="text-muted">ID: ${data.id_no}</small>
                </div>
                <div class="text-end">
                    <small class="text-muted">${data.timestamp}</small>
                </div>
            </div>
        `;
        
        // Replace empty state or add to top
        if (container.innerHTML.includes('No recent submissions')) {
            container.innerHTML = submissionHtml;
        } else {
            container.insertAdjacentHTML('afterbegin', submissionHtml);
            
            // Keep only last 5 submissions
            const submissions = container.querySelectorAll('.border-bottom');
            if (submissions.length > 5) {
                submissions[submissions.length - 1].remove();
            }
        }
    }
    
    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        refreshStatus();
    });
</script>
{% endblock %}
